# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/code-security/dependabot/dependabot-version-updates/configuration-options-for-the-dependabot.yml-file

version: 2

registries:
  main-repo:
    type: python-index
    url: "https://primesec-797808101759.d.codeartifact.eu-central-1.amazonaws.com/pypi/main-repo/simple/"
    username: aws
    password: ${{ secrets.CODE_ARTIFACT_TOKEN_PRIVATE_REPOS }}
    replaces-base: true

updates:
  - package-ecosystem: "pip"
    directory: "/"
    registries:
      - main-repo
    insecure-external-code-execution: allow
    assignees:
      - "matan-prime"
      - "danny-prime"
    groups:
      all-dependencies:
        patterns:
          - "*"
    schedule:
      interval: "weekly"
      day: "sunday"
      time: "10:00"
