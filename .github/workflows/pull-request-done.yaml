name: Service Merge To Main

on:
  pull_request:
    types:
      - closed
    branches:
      - main

permissions:
  contents: write
  id-token: write
  checks: write
  actions: read

jobs:
  open-merge-request:
    if: github.event.pull_request.merged == true
    uses: primesec-ai/common/.github/workflows/eagle-merge-request.yaml@main
    secrets:
      ACTIONS_APP_SECRET: ${{ secrets.ACTIONS_APP_SECRET }}

  publish-client:
    if: github.event.pull_request.merged == true
    uses: primesec-ai/common/.github/workflows/service-publish-client.yaml@main

