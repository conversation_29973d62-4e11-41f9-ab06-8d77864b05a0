from itertools import count

import pytest
from prime_security_review_service_client.models.design_doc_type import DesignDocType
from prime_shared.common_dataclasses import PaginationArgs
from sqlalchemy.exc import NoResultFound

from service.db import DesignDocsTable, ServiceDAL
from service.models.design_docs import DesignDocTopRecommendation

ISSUE_ID = "test_issue_id"

case_id_counter = count(1)  # Start from 1


def get_next_case_id() -> int:
    return next(case_id_counter)


async def create_design_docs_for_testing(service_dal_fixture: ServiceDAL, account_id: str) -> list[DesignDocsTable]:
    docs = [
        await service_dal_fixture.design_docs_dal.add_design_doc(
            account_id, "title", "created_by", origin_id=f"origin_id_{i}", doc_source_type=DesignDocType.ORIGINAL
        )
        for i in range(3)
    ]

    docs.extend(
        [
            await service_dal_fixture.design_docs_dal.add_design_doc(
                account_id, "title", "created_by", url=f"https://example{i}.com", doc_source_type=DesignDocType.URL
            )
            for i in range(3)
        ]
    )

    docs.extend(
        [
            await service_dal_fixture.design_docs_dal.add_design_doc(
                account_id, "title", "created_by", case_id=get_next_case_id(), doc_source_type=DesignDocType.CONTAINER
            )
        ]
    )
    return docs


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestDesignDocsDAL:
    async def test_add_design_doc(self, service_dal_fixture: ServiceDAL, account_id: str):
        design_doc = await service_dal_fixture.design_docs_dal.add_design_doc(
            account_id, "title", "created_by", origin_id="origin_id"
        )
        assert design_doc.title == "title"
        assert design_doc.file_origin_id == "origin_id"

        design_doc = await service_dal_fixture.design_docs_dal.add_design_doc(
            account_id, "title", "created_by", url="https://example.com"
        )
        assert design_doc.title == "title"
        assert design_doc.url == "https://example.com"

        case_id = get_next_case_id()
        design_doc = await service_dal_fixture.design_docs_dal.add_design_doc(
            account_id, "title", "created_by", case_id=case_id
        )
        assert design_doc.case_id == case_id

    async def test_add_design_doc_account_mismatch(self, service_dal_fixture: ServiceDAL, account_id: str):
        case_id = get_next_case_id()
        await service_dal_fixture.design_docs_dal.add_design_doc(account_id, "title", "created_by", case_id=case_id)
        with pytest.raises(ValueError):
            await service_dal_fixture.design_docs_dal.add_design_doc(account_id, "title", "created_by", case_id=case_id)

    async def test_get_design_doc(self, service_dal_fixture: ServiceDAL, account_id: str):
        expected_doc = await service_dal_fixture.design_docs_dal.add_design_doc(
            account_id, "title", "created_by", origin_id="origin_id"
        )
        actual_doc = await service_dal_fixture.design_docs_dal.get_design_doc(account_id, origin_id="origin_id")
        assert actual_doc == expected_doc
        with pytest.raises(NoResultFound):
            await service_dal_fixture.design_docs_dal.get_design_doc(account_id + "2", origin_id="origin_id")

        expected_doc = await service_dal_fixture.design_docs_dal.add_design_doc(
            account_id, "title", "created_by", url="https://example.com"
        )
        actual_doc = await service_dal_fixture.design_docs_dal.get_design_doc(account_id, url="https://example.com")
        assert actual_doc == expected_doc
        with pytest.raises(NoResultFound):
            await service_dal_fixture.design_docs_dal.get_design_doc(account_id, url="https://example2.com")
        with pytest.raises(NoResultFound):
            await service_dal_fixture.design_docs_dal.get_design_doc(account_id + "2", url="https://example2.com")

        case_id = get_next_case_id()
        expected_doc = await service_dal_fixture.design_docs_dal.add_design_doc(
            account_id,
            "title",
            "created_by",
            case_id=case_id,
        )
        actual_doc = await service_dal_fixture.design_docs_dal.get_design_doc(account_id, case_id=case_id)
        assert actual_doc == expected_doc
        with pytest.raises(NoResultFound):
            await service_dal_fixture.design_docs_dal.get_design_doc(account_id, case_id=99999)
        with pytest.raises(NoResultFound):
            await service_dal_fixture.design_docs_dal.get_design_doc(account_id + "2", case_id=case_id)

    async def test_get_design_doc_by_id(self, service_dal_fixture: ServiceDAL, account_id: str):
        expected_doc = await service_dal_fixture.design_docs_dal.add_design_doc(
            account_id, "title", "created_by", origin_id="origin_id"
        )
        actual_doc = await service_dal_fixture.design_docs_dal.get_design_docs_by_id(account_id, doc_id=expected_doc.id)
        assert expected_doc == actual_doc

    async def test_get_no_design_docs(self, service_dal_fixture: ServiceDAL, account_id: str):
        docs = await service_dal_fixture.design_docs_dal.get_design_docs(account_id)
        assert len(docs) == 0

    async def test_get_design_docs(self, service_dal_fixture: ServiceDAL, account_id: str):
        expected_docs = await create_design_docs_for_testing(service_dal_fixture, account_id)
        docs = await service_dal_fixture.design_docs_dal.get_design_docs(account_id)
        assert len(docs) == len(expected_docs)

        limit = 2
        docs = await service_dal_fixture.design_docs_dal.get_design_docs(
            account_id, pagination_args=PaginationArgs(limit=limit, offset=0)
        )
        assert len(docs) == limit

        results = []
        pagination = PaginationArgs(limit=limit, offset=0)
        while True:
            batch_results = await service_dal_fixture.design_docs_dal.get_design_docs(
                account_id, pagination_args=pagination
            )
            if not batch_results:
                break
            pagination.offset += len(batch_results)
            results.extend(batch_results)
        assert len(results) == len(expected_docs)

    async def test_update_design_doc_by_case_id(self, service_dal_fixture: ServiceDAL, account_id: str):
        docs = await create_design_docs_for_testing(service_dal_fixture, account_id)
        design_doc_recommendations = [
            DesignDocTopRecommendation(
                id=1,
                title="test_title1",
                description="test_description1",
                references=[1],
                component="my-component1",
                concern="my-concern1",
                evidence="my-evidence1",
                quotes=["some-quote-text1", "some-quote-text2"],
                attack_scenarios_names=["attack-scenario-1", "attack-scenario-2"],
            ),
            DesignDocTopRecommendation(
                id=2,
                title="test_title2",
                description="test_description2",
                references=[2, 3],
                component="my-component2",
                concern="my-concern2",
                evidence="my-evidence2",
                quotes=["some-quote-text3", "some-quote-text4"],
                attack_scenarios_names=["attack-scenario-3", "attack-scenario-4"],
            ),
        ]
        await service_dal_fixture.design_docs_dal.update_design_doc(
            account_id,
            design_doc_id=docs[0].id,
            top_recommendations=design_doc_recommendations,
        )
        actual_doc = await service_dal_fixture.design_docs_dal.get_design_docs_by_id(account_id, docs[0].id)
        assert actual_doc.top_recommendations == design_doc_recommendations
