import warnings
from pathlib import Path

from prime_logger import LoggingSettings, init_root_logger
from prime_tests import set_test_env
from prime_tests.db_fixtures_import import set_db_testing

set_test_env(Path(__file__).parent / "test.env")  # this needs to be before the ServiceConfig initialization

init_root_logger(LoggingSettings(json_log_enabled=False))
with warnings.catch_warnings(action="ignore"):
    from .fixtures import *  # noqa: F403

pytest_plugins = set_db_testing()
