import contextlib
import json
import random
from datetime import UTC, datetime
from unittest.mock import Mock

import pytest
from prime_fetcher_service_client import SourceType as FetcherSourceType
from prime_fetcher_service_client import UrlMetadata
from prime_file_manager_service_client import DocumentType, FileInfo, PaginationResponseFileInfo
from prime_jobs import JobCancelReason, JobStatus, UpdateJob
from prime_redis_utils import AsyncPrefixRedisClient
from prime_security_review_service_client import (
    AttachContextToDesignDocReq,
    BodyCreateDesignDocContainer,
    BodyReprocessDesignDoc,
    ContextType,
    DesignDocByMultipleUrlsReq,
    DesignDocBySingleUrlReq,
    DesignDocFileUploadResponse,
    DesignDocProcessStatus,
    DesignDocResponse,
    DesignDocsApi,
    JobsApi,
)
from prime_security_review_service_client.exceptions import BadRequestException, ConflictException, NotFoundException
from prime_shared.common_values import FETCHER_METADATA_HEADER
from prime_tests import <PERSON><PERSON><PERSON><PERSON>ponse, ServiceMocked, service_mocker
from prime_utils import unzip_data

from service.db import DesignDocsTable, ServiceDAL
from service.errors import DesignDocNotFoundError
from service.errors.errors import DesignDocUpdateNotAllowed
from service.jobs.design_docs_job import DesignDocsJobArgs
from service.jobs.design_docs_job.utils import get_redis_processing_progress_percent_key
from service.jobs.job_spawners import DesignDocsJobSpawner
from service.logic import _sanitize_filename
from tests.db.test_design_docs_dal import _create_design_docs
from tests.mock_utils import (
    DESIGN_DOCS_SOURCE_ID,
    _agent_api_mocks,
    _basic_mocks,
    get_external_cases,
    mock_cases_data,
    mock_source_service,
    resources_dir,
)

from ..jobs.job_test_utils import mock_job_creation, validate_job_creation


@contextlib.contextmanager
def _mock_source_url_response(urls: dict[str, tuple[bytes, UrlMetadata]], account_id: str):
    with (
        service_mocker("file-manager-service") as file_manager_mocker,
        mock_source_service(account_id),
        service_mocker("fetcher-service") as fetcher_mocker,
    ):
        file_manager_mocker.put(f"/files/{account_id}/zip", MockResponse(None))
        for url, (file_data, metadata_model) in urls.items():
            metadata = metadata_model.model_dump_json()
            source_type = FetcherSourceType.GOOGLE if "google.com" in url else FetcherSourceType.JIRA

            escaped_url = url.replace(":", "%3A")
            fetcher_mocker.get(
                f"download/{account_id}?url={escaped_url}*",
                MockResponse(file_data, headers={FETCHER_METADATA_HEADER: metadata, "prime-source-type": source_type}),
            )
        yield file_manager_mocker


async def _validate_db_and_response(
    design_doc_db: DesignDocsTable,
    design_doc_response: DesignDocResponse,
    processing_status: DesignDocProcessStatus | None = None,
) -> None:
    assert design_doc_db.title == design_doc_response.title
    assert design_doc_db.file_origin_id == design_doc_response.file_origin_id
    assert design_doc_db.updated_at == design_doc_response.updated_at
    assert design_doc_response.url == design_doc_db.url
    assert design_doc_db.case_id == design_doc_response.case_id
    if processing_status:
        assert design_doc_response.processing_status is not None
        assert design_doc_response.processing_status.job_status == processing_status.job_status
        assert (
            design_doc_response.processing_status.processing_progress_percent
            == processing_status.processing_progress_percent
        )
        assert design_doc_response.processing_status.timestamp is not None


async def _validate_job_creation(
    account_id: str,
    design_doc: list[DesignDocsTable],
    deploy_helm_mock: Mock,
    jobs_api: JobsApi,
):
    job_id = max([job.id for job in await jobs_api.get_jobs(account_id)])
    expected_job_args = DesignDocsJobArgs(
        account_id=account_id, job_id=job_id, force=False, design_doc_ids=[doc.id for doc in design_doc]
    )
    spawner = DesignDocsJobSpawner(**expected_job_args.model_dump(exclude={"job_id"}))
    await validate_job_creation(spawner, expected_job_args, deploy_helm_mock)


async def _validate_job_creation_and_file_manager_calls(
    account_id: str,
    design_doc: list[DesignDocsTable],
    file_manager_mocker: ServiceMocked,
    deploy_helm_mock: Mock,
    jobs_api: JobsApi,
):
    await _validate_job_creation(account_id, design_doc, deploy_helm_mock, jobs_api)
    unzipped_request = unzip_data(file_manager_mocker.requests["PUT"][f"files/{account_id}/zip"][0].args[3])
    assert len(unzipped_request) == 2


async def _validate_results(
    design_doc: list[DesignDocsTable],
    expected_origin_ids: list[str],
    expected_titles: list[str],
    response: DesignDocFileUploadResponse,
):
    assert response.files_uploaded == len(design_doc)
    assert set(response.file_names) == {doc.title for doc in design_doc} == set(expected_titles)
    assert {doc.file_origin_id for doc in design_doc} == set(expected_origin_ids)


async def _set_processing_progress_percent(account_redis_client, design_docs, progress_mapping):
    for doc in design_docs:
        await account_redis_client.set(
            get_redis_processing_progress_percent_key(doc.id),
            DesignDocProcessStatus(
                processing_progress_percent=progress_mapping[doc.id],
                job_status=JobStatus.RUNNING,
                timestamp=datetime.now(UTC),
            ).model_dump_json(),
        )


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestDesignDocs:
    async def test_get_design_docs(
        self, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    ):
        expected_design_docs = await _create_design_docs(service_dal_fixture, account_id)

        case_ids = [design_doc.case_id for design_doc in expected_design_docs if design_doc.case_id is not None]
        with mock_cases_data(account_id, case_ids):
            actual_design_docs = await design_docs_api.get_design_docs(account_id)

            result_design_docs = actual_design_docs.results
            created_at_list = [doc.created_at for doc in result_design_docs]
            assert created_at_list == sorted(
                created_at_list, reverse=True
            ), "Design docs are not in descending order by created_at"

            expected_design_docs.sort(key=lambda d: d.created_at, reverse=True)
            for expected, actual in zip(expected_design_docs, result_design_docs, strict=False):
                await _validate_db_and_response(expected, actual)

    async def test_get_design_docs_processing_progress(
        self,
        service_dal_fixture: ServiceDAL,
        design_docs_api: DesignDocsApi,
        account_id: str,
        account_redis_client: AsyncPrefixRedisClient,
    ):
        expected_design_docs = await _create_design_docs(service_dal_fixture, account_id)

        processing_progress_percent_mapping = {
            expected_design_doc.id: random.uniform(0, 100) for expected_design_doc in expected_design_docs
        }
        await _set_processing_progress_percent(
            account_redis_client, expected_design_docs, processing_progress_percent_mapping
        )

        case_ids = [design_doc.case_id for design_doc in expected_design_docs if design_doc.case_id is not None]
        with mock_cases_data(account_id, case_ids):
            actual_design_docs = await design_docs_api.get_design_docs(account_id)

            expected_design_docs.sort(key=lambda d: d.created_at, reverse=True)
            for expected, actual in zip(expected_design_docs, actual_design_docs.results, strict=False):
                await _validate_db_and_response(
                    expected,
                    actual,
                    DesignDocProcessStatus(
                        processing_progress_percent=processing_progress_percent_mapping[expected.id],
                        job_status=JobStatus.RUNNING,
                        timestamp=datetime.now(UTC),
                    ),
                )

    async def test_get_design_docs_with_pagination(
        self, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    ):
        expected_design_docs = await _create_design_docs(service_dal_fixture, account_id)
        expected_design_docs.sort(key=lambda d: d.created_at, reverse=True)

        case_ids = [design_doc.case_id for design_doc in expected_design_docs if design_doc.case_id is not None]
        with mock_cases_data(account_id, case_ids):
            actual_design_docs = (await design_docs_api.get_design_docs(account_id, limit=3, offset=2)).results
            for expected, actual in zip(expected_design_docs[2 : 2 + 3], actual_design_docs, strict=False):
                await _validate_db_and_response(expected, actual)

            design_docs = await design_docs_api.get_design_docs(account_id, limit=50, offset=2)
            assert len(design_docs.results) == len(expected_design_docs) - 2
            assert design_docs.total == len(expected_design_docs)

    async def test_get_design_doc_by_id(
        self, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    ) -> None:
        expected_design_docs = await _create_design_docs(service_dal_fixture, account_id)
        case_ids = [design_doc.case_id for design_doc in expected_design_docs if design_doc.case_id is not None]
        with mock_cases_data(account_id, case_ids):
            for design_doc_db in expected_design_docs:
                extended_design_doc = await design_docs_api.get_design_doc_by_id(account_id, design_doc_db.id)
                await _validate_db_and_response(design_doc_db, extended_design_doc)
                assert design_doc_db.summary or extended_design_doc.summary == ""
                assert design_doc_db.mermaid_diagram == extended_design_doc.mermaid_diagram
                assert design_doc_db.top_recommendations or extended_design_doc.top_recommendations == []
                assert design_doc_db.policy_recommendations or extended_design_doc.policy_recommendations == []
                assert design_doc_db.attack_scenarios or extended_design_doc.attack_scenarios == []

    async def test_download_design_doc(
        self, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    ) -> None:
        file_binary_data = b"binary_data"
        with _basic_mocks() as (file_manager_mocker, _, _):
            expected_design_docs = await _create_design_docs(service_dal_fixture, account_id)
            for design_docs_db in expected_design_docs:
                design_doc = await service_dal_fixture.design_docs_dal.get_design_docs_by_id(
                    account_id, design_docs_db.id
                )
                if design_doc.file_origin_id is None:
                    continue

                file_manager_mocker.get(
                    f"/files/{account_id}/data/source/{DESIGN_DOCS_SOURCE_ID}/origin_id/{design_doc.file_origin_id}",
                    MockResponse(content=file_binary_data),
                )
                data = await design_docs_api.download_design_doc(account_id, design_doc.id)
                assert data == file_binary_data

    async def test_delete_design_doc(
        self, design_docs_api: DesignDocsApi, service_dal_fixture: ServiceDAL, account_id: str
    ) -> None:
        expected_design_docs = await _create_design_docs(service_dal_fixture, account_id)
        case_ids = [design_doc.case_id for design_doc in expected_design_docs if design_doc.case_id is not None]
        with mock_cases_data(account_id, case_ids):
            total_docs = len(expected_design_docs)
            for doc in expected_design_docs:
                await design_docs_api.delete_design_doc(account_id, doc.id)
                total_docs -= 1
                design_docs = await design_docs_api.get_design_docs(account_id)
                assert len(design_docs.results) == total_docs
                with pytest.raises(NotFoundException) as e:
                    await design_docs_api.get_design_doc_by_id(account_id, doc.id)
                assert json.loads(e.value.body)["exception"] == repr(DesignDocNotFoundError(doc.id))

    async def test_reprocess_design_doc(
        self, jobs_api: JobsApi, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    ):
        expected_design_docs = await _create_design_docs(service_dal_fixture, account_id)
        doc_to_reprocess = expected_design_docs[0]
        with service_mocker("file-manager-service"), mock_source_service(account_id):
            async with mock_job_creation() as deploy_helm_mock:
                await design_docs_api.reprocess_design_doc(
                    account_id,
                    doc_to_reprocess.id,
                    BodyReprocessDesignDoc(created_by="test_user_id"),
                )

            await _validate_job_creation(account_id, [doc_to_reprocess], deploy_helm_mock, jobs_api)

            design_docs = await service_dal_fixture.design_docs_dal.get_design_docs(account_id)
            assert len(expected_design_docs) == len(design_docs)

    async def test_attach_context_to_design_doc(
        self, design_docs_api: DesignDocsApi, service_dal_fixture: ServiceDAL, account_id: str, jobs_api: JobsApi
    ):
        conversation_id_to_attach = 1
        expected_design_docs = await _create_design_docs(service_dal_fixture, account_id)
        doc_to_attach_context = expected_design_docs[0]
        assert doc_to_attach_context.agent_conversation_id is None
        with (
            _agent_api_mocks(conv_id=conversation_id_to_attach),
            service_mocker("file-manager-service"),
            mock_source_service(account_id),
        ):
            async with mock_job_creation() as deploy_helm_mock:
                await design_docs_api.attach_context_to_design_doc(
                    account_id,
                    doc_to_attach_context.id,
                    AttachContextToDesignDocReq(
                        created_by="test_user_id", context_type=ContextType.AGENT, context_id=conversation_id_to_attach
                    ),
                )
                actual_doc = await service_dal_fixture.design_docs_dal.get_design_docs_by_id(
                    account_id, doc_to_attach_context.id
                )
                assert actual_doc.agent_conversation_id == conversation_id_to_attach
                await _validate_job_creation(account_id, [actual_doc], deploy_helm_mock, jobs_api)
                with pytest.raises(BadRequestException) as exc_info:
                    await design_docs_api.attach_context_to_design_doc(
                        account_id,
                        doc_to_attach_context.id,
                        AttachContextToDesignDocReq(
                            created_by="test_user_id",
                            context_type=ContextType.AGENT,
                            context_id=conversation_id_to_attach + 1,
                        ),
                    )
            assert exc_info.value.status == 400

    async def test_design_docs_by_file(
        self, jobs_api: JobsApi, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    ):
        file_data = b"This is a test design document"
        test_filename = "Daily - All - 2025/01/20 14:30 IST - Transcript"
        design_doc_file = [(test_filename, file_data)]
        with service_mocker("file-manager-service") as file_manager_mocker, mock_source_service(account_id):
            res = PaginationResponseFileInfo(results=[], size=1, limit=1, start=0)
            file_manager_mocker.put(
                f"/files/{account_id}/info?source-id=-2", MockResponse(res.model_dump(), status_code=200)
            )
            file_manager_mocker.put(f"/files/{account_id}/zip", MockResponse(None, status_code=200))
            async with mock_job_creation() as deploy_helm_mock:
                response = await design_docs_api.design_docs_by_file(
                    account_id,
                    created_by="test_user_id",
                    design_doc_files=design_doc_file,
                )
            design_docs = await service_dal_fixture.design_docs_dal.get_design_docs(account_id)
            await _validate_job_creation_and_file_manager_calls(
                account_id, design_docs, file_manager_mocker, deploy_helm_mock, jobs_api
            )
            await _validate_results(design_docs, [test_filename], [test_filename], response)
            assert design_docs[0].title == test_filename

    async def test_design_docs_by_multiple_file(
        self, jobs_api: JobsApi, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    ):
        file_data1 = (resources_dir / "sample1.pdf").read_bytes()
        test_filename1 = "Daily - All - 2025/01/20 14:30 IST - Transcript"
        file_data2 = (resources_dir / "sample2.pdf").read_bytes()
        test_filename2 = "filename2"
        design_doc_files = [(test_filename1, file_data1), (test_filename2, file_data2)]
        with service_mocker("file-manager-service") as file_manager_mocker, mock_source_service(account_id):
            res = PaginationResponseFileInfo(results=[], size=1, limit=1, start=0)
            file_manager_mocker.put(
                f"/files/{account_id}/info?source-id=-2", MockResponse(res.model_dump(), status_code=200)
            )
            # Validate that the files are processed separately
            file_manager_mocker.put(f"/files/{account_id}/zip", MockResponse(None, status_code=200))
            async with mock_job_creation() as deploy_helm_mock:
                response = await design_docs_api.design_docs_by_file(
                    account_id,
                    created_by="test_user_id",
                    design_doc_files=design_doc_files,
                )
            design_docs = await service_dal_fixture.design_docs_dal.get_design_docs(account_id)
            origin_ids = [test_filename1, test_filename2]
            await _validate_job_creation_and_file_manager_calls(
                account_id, design_docs, file_manager_mocker, deploy_helm_mock, jobs_api
            )
            await _validate_results(design_docs, origin_ids, origin_ids, response)
            # Validate that the files are processed as one
            async with mock_job_creation() as deploy_helm_mock:
                response = await design_docs_api.design_docs_by_file(
                    account_id,
                    created_by="test_user_id",
                    design_doc_files=design_doc_files,
                    process_as_one=True,
                )
                origin_ids = [test_filename1 + " (+1)"]
                design_doc = await service_dal_fixture.design_docs_dal.get_design_doc(
                    account_id, origin_id=response.file_names[0]
                )
                await _validate_job_creation_and_file_manager_calls(
                    account_id, [design_doc], file_manager_mocker, deploy_helm_mock, jobs_api
                )
                await _validate_results([design_doc], origin_ids, origin_ids, response)

    async def test_design_docs_by_gdrive_url(
        self, jobs_api: JobsApi, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    ):
        file_data = b"This is a test design document"
        now = datetime.now(UTC)
        metadata = UrlMetadata(
            id="1",
            name="test_file",
            modified_time=now,
        )
        url = "https://drive.google.com/file/d/1a2b3c4d5e6f7g8h9/view"
        urls = {url: (file_data, metadata)}
        with _mock_source_url_response(urls, account_id) as file_manager_mocker:
            res = PaginationResponseFileInfo(results=[], size=1, limit=1, start=0)
            file_manager_mocker.put(
                f"/files/{account_id}/info?source-id=-2", MockResponse(res.model_dump(), status_code=200)
            )
            async with mock_job_creation() as deploy_helm_mock:
                response = await design_docs_api.design_docs_by_gdrive(
                    account_id,
                    DesignDocBySingleUrlReq(url=url, created_by="test_user_id"),
                )
            origin_id = response.file_names[0]
            design_doc = await service_dal_fixture.design_docs_dal.get_design_doc(account_id, url=url)
            await _validate_job_creation_and_file_manager_calls(
                account_id, [design_doc], file_manager_mocker, deploy_helm_mock, jobs_api
            )
            await _validate_results([design_doc], [origin_id], [origin_id], response)
            assert design_doc.title == metadata.name

    async def test_design_docs_by_confluence_url(
        self, jobs_api: JobsApi, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    ):
        file_data = b"This is a test design document"
        now = datetime.now(UTC)
        metadata = UrlMetadata(id="1", name="test_file", modified_time=now)
        url = "https://your-domain.atlassian.net/wiki/spaces/TEST/pages/*********/Design_Document"
        urls = {url: (file_data, metadata)}
        with _mock_source_url_response(urls, account_id) as file_manager_mocker:
            res = PaginationResponseFileInfo(results=[], size=1, limit=1, start=0)
            file_manager_mocker.put(
                f"/files/{account_id}/info?source-id=-2", MockResponse(res.model_dump(), status_code=200)
            )
            async with mock_job_creation() as deploy_helm_mock:
                response = await design_docs_api.design_docs_by_confluence(
                    account_id,
                    DesignDocBySingleUrlReq(url=url, created_by="test_user_id"),
                )
            origin_id = response.file_names[0]
            design_doc = await service_dal_fixture.design_docs_dal.get_design_doc(account_id, url=url)
            await _validate_job_creation_and_file_manager_calls(
                account_id, [design_doc], file_manager_mocker, deploy_helm_mock, jobs_api
            )
            await _validate_results([design_doc], [origin_id], [origin_id], response)
            assert design_doc.title == metadata.name

    async def test_design_docs_by_urls(
        self, jobs_api: JobsApi, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    ):
        file_data1 = (resources_dir / "sample1.pdf").read_bytes()
        file_data2 = (resources_dir / "sample2.pdf").read_bytes()
        url1 = "https://example.com/doc1"
        url2 = "https://example.com/doc2"
        mock_urls = {
            url1: (file_data1, UrlMetadata(id="1", name="doc1", modified_time=datetime.now(UTC))),
            url2: (file_data2, UrlMetadata(id="2", name="doc2", modified_time=datetime.now(UTC))),
        }
        urls = [url1, url2]
        urls_request = DesignDocByMultipleUrlsReq(urls=urls, created_by="test_user_id", process_as_one=False)

        with _mock_source_url_response(mock_urls, account_id) as file_manager_mocker:
            res = PaginationResponseFileInfo(results=[], size=1, limit=1, start=0)
            file_manager_mocker.put(
                f"/files/{account_id}/info?source-id=-2", MockResponse(res.model_dump(), status_code=200)
            )
            async with mock_job_creation() as deploy_helm_mock:
                response = await design_docs_api.design_docs_by_urls(account_id, urls_request)
            origin_ids = ["doc1", "doc2"]
            design_docs = [
                await service_dal_fixture.design_docs_dal.get_design_doc(account_id, origin_id=origin)
                for origin in response.file_names
            ]
            await _validate_job_creation_and_file_manager_calls(
                account_id, design_docs, file_manager_mocker, deploy_helm_mock, jobs_api
            )
            await _validate_results(design_docs, origin_ids, origin_ids, response)
            # Validate processing as one
            async with mock_job_creation() as deploy_helm_mock:
                urls_request.process_as_one = True
                response = await design_docs_api.design_docs_by_urls(account_id, urls_request)
            origin_id = _sanitize_filename(";".join(origin_ids))
            design_docs = [await service_dal_fixture.design_docs_dal.get_design_doc(account_id, origin_id=origin_id)]
            await _validate_job_creation_and_file_manager_calls(
                account_id, design_docs, file_manager_mocker, deploy_helm_mock, jobs_api
            )
            await _validate_results(design_docs, [origin_id], ["doc1 (+1)"], response)

    async def test_design_docs_by_container(
        self, jobs_api: JobsApi, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    ) -> None:
        case = get_external_cases(account_id, [1])[0]
        with (
            mock_source_service(account_id),
            service_mocker("rat-logic-service") as rat_logic_mocker,
        ):
            rat_logic_mocker.get(f"/cases/{account_id}/case/{case.case_id}", MockResponse(case.model_dump()))
            async with mock_job_creation():
                body = BodyCreateDesignDocContainer(created_by="test_user_id")
                await design_docs_api.create_design_doc_container(account_id, case.case_id, body)
            design_doc = await service_dal_fixture.design_docs_dal.get_design_doc(account_id, case_id=case.case_id)
            assert design_doc.title == case.title
            assert design_doc.case_id == case.case_id

    async def test_get_security_review_by_container_success(
        self, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    ) -> None:
        """Test successful retrieval of security review by container"""
        case = get_external_cases(account_id, [1])[0]

        # First create a design doc container
        with (
            mock_source_service(account_id),
            service_mocker("rat-logic-service") as rat_logic_mocker,
        ):
            rat_logic_mocker.get(f"/cases/{account_id}/case/{case.case_id}", MockResponse(case.model_dump()))
            async with mock_job_creation():
                body = BodyCreateDesignDocContainer(created_by="test_user_id")
                await design_docs_api.create_design_doc_container(account_id, case.case_id, body)

            # Mock the cases API call for the GET endpoint (URL encoded filter)
            cases_filter_encoded = "%5B%7B%22field%22%3A%22id%22%2C%22op%22%3A%22eq%22%2C%22value%22%3A%5B%221%22%5D%7D%5D"
            rat_logic_mocker.get(
                f"/cases/{account_id}?f={cases_filter_encoded}",
                MockResponse({"results": [case.model_dump()], "size": 1, "limit": 10, "start": 0})
            )

            # Test the GET endpoint using direct HTTP call since client method doesn't exist yet
            from starlette.testclient import TestClient
            from service.service_app import app

            client = TestClient(app)
            response = client.get(f"/design-docs/{account_id}/case-id/{case.case_id}")

            assert response.status_code == 200
            response_data = response.json()

            # Verify response structure and content
            assert response_data["title"] == case.title
            assert response_data["case_id"] == case.case_id
            assert response_data["created_by"] == "test_user_id"
            assert response_data["doc_source_type"] == "CONTAINER"
            assert "id" in response_data
            assert "created_at" in response_data
            assert "updated_at" in response_data



    async def test_design_docs_by_file_duplicate_error(self, design_docs_api: DesignDocsApi, account_id: str):
        file_data = b"This is a test design document"
        duplicate_filename1 = "test design doc duplicate 1"
        duplicate_filename2 = "test design doc 2"
        design_doc_files = [(duplicate_filename1, file_data), (duplicate_filename2, file_data)]

        with service_mocker("file-manager-service") as file_manager_mocker, mock_source_service(account_id):
            file_info = [
                FileInfo(
                    origin_id=_sanitize_filename(duplicate_filename1),
                    domain="",
                    downloadable_link="",
                    document_type=DocumentType.DESIGN_DOC,
                    id=0,
                    timestamp=None,
                    source_id=None,
                ),
            ]
            res_with_files = PaginationResponseFileInfo(results=file_info, size=1, limit=2, start=0)

            file_manager_mocker.put(
                f"/files/{account_id}/info?source-id=-2", MockResponse(res_with_files.model_dump(), status_code=200)
            )

            with pytest.raises(ConflictException) as exc_info:
                await design_docs_api.design_docs_by_file(
                    account_id, created_by="test", design_doc_files=design_doc_files
                )

            error_detail = json.loads(json.loads(exc_info.value.body)["detail"])
            assert duplicate_filename1 in error_detail
            assert duplicate_filename2 not in error_detail

    async def test_design_docs_by_gdrive_duplicate_error(self, design_docs_api: DesignDocsApi, account_id: str):
        file_data = b"This is a test design document"
        now = datetime.now(UTC)
        metadata = UrlMetadata(id="1", name="test_file", modified_time=now)
        url = "https://drive.google.com/file/d/1a2b3c4d5e6f7g8h9/view"

        with (
            service_mocker("file-manager-service") as file_manager_mocker,
            mock_source_service(account_id),
            service_mocker("fetcher-service") as fetcher_mocker,
        ):
            escaped_url = url.replace(":", "%3A")
            fetcher_mocker.get(
                f"download/{account_id}?url={escaped_url}&pdf_format=true",
                MockResponse(file_data, headers={FETCHER_METADATA_HEADER: metadata.model_dump_json()}),
            )
            fetcher_mocker.get(
                f"download/{account_id}?&url={escaped_url}",
                MockResponse(metadata.model_dump(), headers={FETCHER_METADATA_HEADER: metadata.model_dump_json()}),
            )

            file_info = [
                FileInfo(
                    origin_id=_sanitize_filename(metadata.name),
                    domain="",
                    downloadable_link="",
                    document_type=DocumentType.DESIGN_DOC,
                    id=0,
                    timestamp=None,
                    source_id=None,
                )
            ]
            res_with_files = PaginationResponseFileInfo(results=file_info, size=1, limit=1, start=0)
            file_manager_mocker.put(
                f"/files/{account_id}/info?source-id=-2", MockResponse(res_with_files.model_dump(), status_code=200)
            )

            with pytest.raises(ConflictException):
                await design_docs_api.design_docs_by_gdrive(
                    account_id, DesignDocBySingleUrlReq(created_by="test", url=url)
                )

    async def test_design_docs_by_confluence_duplicate_error(self, design_docs_api: DesignDocsApi, account_id: str):
        file_data = b"This is a test design document"
        now = datetime.now(UTC)
        metadata = UrlMetadata(id="1", name="test_file", modified_time=now)
        url = "https://your-domain.atlassian.net/wiki/spaces/TEST/pages/*********/Design_Document"

        with (
            service_mocker("file-manager-service") as file_manager_mocker,
            mock_source_service(account_id),
            service_mocker("fetcher-service") as fetcher_mocker,
        ):
            escaped_url = url.replace(":", "%3A")
            fetcher_mocker.get(
                f"download/{account_id}?url={escaped_url}",
                MockResponse(file_data, status_code=200),
            )
            fetcher_mocker.get(
                f"download/{account_id}*",
                MockResponse(
                    content=b"some-fake-content",
                    status_code=200,
                    headers={FETCHER_METADATA_HEADER: metadata.model_dump_json()},
                ),
            )

            file_info = [
                FileInfo(
                    origin_id=_sanitize_filename(metadata.name),
                    domain="",
                    downloadable_link="",
                    document_type=DocumentType.DESIGN_DOC,
                    id=0,
                    timestamp=None,
                    source_id=None,
                )
            ]
            res_with_files = PaginationResponseFileInfo(results=file_info, size=1, limit=1, start=0)
            file_manager_mocker.put(
                f"/files/{account_id}/info?source-id=-2", MockResponse(res_with_files.model_dump(), status_code=200)
            )

            with pytest.raises(ConflictException):
                await design_docs_api.design_docs_by_confluence(
                    account_id, DesignDocBySingleUrlReq(created_by="test", url=url)
                )

    async def test_update_design_doc_by_file(
        self, jobs_api: JobsApi, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    ):
        original_file_data = b"Original PDF content"
        original_filename = "original.pdf"
        design_doc_file = [(original_filename, original_file_data)]

        with service_mocker("file-manager-service") as file_manager_mocker, mock_source_service(account_id):
            res = PaginationResponseFileInfo(results=[], size=1, limit=1, start=0)
            file_manager_mocker.put(
                f"/files/{account_id}/info?source-id=-2", MockResponse(res.model_dump(), status_code=200)
            )
            file_manager_mocker.put(f"/files/{account_id}/zip", MockResponse({}, status_code=200))
            async with mock_job_creation():
                await design_docs_api.design_docs_by_file(
                    account_id, created_by="test", design_doc_files=design_doc_file
                )

        # Get the created design doc
        design_docs = await service_dal_fixture.design_docs_dal.get_design_docs(account_id)
        assert len(design_docs) == 1
        original_doc = design_docs[0]
        original_doc_id = original_doc.id
        original_title = original_doc.title
        original_origin_id = original_doc.file_origin_id

        # Add some analysis results to simulate processed doc
        await service_dal_fixture.design_docs_dal.update_design_doc_fields(
            original_doc_id,
            account_id,
            summary="Original summary",
            mermaid_diagram="Original diagram",
        )
        # Mark the job as completed to allow the update to create a new job
        jobs = await service_dal_fixture.scheduler_dal.jobs_dal.get_jobs(account_id=account_id)
        assert len(jobs) == 1
        await service_dal_fixture.scheduler_dal.jobs_dal.update_job(
            account_id=account_id, job_id=jobs[0].id, update_job=UpdateJob(status=JobStatus.COMPLETED)
        )

        # Now update with a new file
        updated_file_data = b"Updated PDF content"
        updated_filename = "updated.pdf"
        update_file = (updated_filename, updated_file_data)

        with service_mocker("file-manager-service") as file_manager_mocker, mock_source_service(account_id):
            # Mock file exists for update (allow_update=True should handle this)
            file_info = [
                FileInfo(
                    origin_id=str(original_origin_id) if original_origin_id else "",
                    domain="",
                    downloadable_link="",
                    document_type=DocumentType.DESIGN_DOC,
                    id=0,
                    timestamp=None,
                    source_id=None,
                )
            ]
            res_with_files = PaginationResponseFileInfo(results=file_info, size=1, limit=1, start=0)
            file_manager_mocker.put(
                f"/files/{account_id}/info?source-id=-2", MockResponse(res_with_files.model_dump(), status_code=200)
            )
            file_manager_mocker.put(f"/files/{account_id}/zip", MockResponse({}, status_code=200))
            async with mock_job_creation() as deploy_helm_mock:
                response = await design_docs_api.update_design_doc_by_file(account_id, original_doc_id, update_file)

        # Verify the response
        assert response.files_uploaded == 1
        assert response.file_names == [original_title]  # Should preserve original title

        # Verify the doc was updated in the database
        service_dal_fixture.session.expire_all()
        updated_doc = await service_dal_fixture.design_docs_dal.get_design_docs_by_id(account_id, original_doc_id)
        assert updated_doc.id == original_doc_id  # Same ID
        assert updated_doc.title == original_title  # Title preserved
        assert updated_doc.file_origin_id == original_origin_id  # Same origin ID

        # Verify analysis results were cleared
        assert updated_doc.summary is None
        assert updated_doc.mermaid_diagram is None
        assert updated_doc.top_recommendations is None
        assert updated_doc.policy_recommendations is None
        assert updated_doc.attack_scenarios is None
        assert updated_doc.attack_scenario_dataflow_diagram is None

        # Verify job was created for reprocessing
        await _validate_job_creation(account_id, [updated_doc], deploy_helm_mock, jobs_api)

        # Verify we still have only one design doc (updated, not created new)
        all_docs = await service_dal_fixture.design_docs_dal.get_design_docs(account_id)
        assert len(all_docs) == 1

    async def test_update_design_doc_by_file_url_based_doc_error(
        self, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    ):
        url = "https://example.com/doc"
        design_doc = await service_dal_fixture.design_docs_dal.add_design_doc(
            account_id, "URL Doc", "test_user", url=url
        )

        updated_file_data = b"Updated PDF content"
        updated_filename = "updated.pdf"
        update_file = (updated_filename, updated_file_data)

        with pytest.raises(BadRequestException) as exc_info:
            await design_docs_api.update_design_doc_by_file(account_id, design_doc.id, update_file)

        assert json.loads(exc_info.value.body)["title"] == DesignDocUpdateNotAllowed.__name__

    @pytest.mark.usefixtures("service_dal_fixture")
    async def test_update_design_doc_by_file_nonexistent_doc(self, design_docs_api: DesignDocsApi, account_id: str):
        updated_file_data = b"Updated PDF content"
        updated_filename = "updated.pdf"
        update_file = (updated_filename, updated_file_data)

        not_existing_doc_id = 99999
        with pytest.raises(NotFoundException) as e:
            await design_docs_api.update_design_doc_by_file(account_id, not_existing_doc_id, update_file)
        assert json.loads(e.value.body)["exception"] == repr(DesignDocNotFoundError(not_existing_doc_id))

    async def test_update_design_doc_by_file_error_starting_job(
        self, jobs_api: JobsApi, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    ):
        original_file_data = b"Original PDF content"
        original_filename = "original.pdf"
        design_doc_file = [(original_filename, original_file_data)]

        with service_mocker("file-manager-service") as file_manager_mocker, mock_source_service(account_id):
            res = PaginationResponseFileInfo(results=[], size=1, limit=1, start=0)
            file_manager_mocker.put(
                f"/files/{account_id}/info?source-id=-2", MockResponse(res.model_dump(), status_code=200)
            )
            file_manager_mocker.put(f"/files/{account_id}/zip", MockResponse({}, status_code=200))
            async with mock_job_creation():
                await design_docs_api.design_docs_by_file(
                    account_id, created_by="test", design_doc_files=design_doc_file
                )

        # # Get the created design doc
        design_docs = await service_dal_fixture.design_docs_dal.get_design_docs(account_id)
        original_doc = design_docs[0]

        # Now update with a new file
        updated_file_data = b"Updated PDF content"
        updated_filename = "updated.pdf"
        update_file = (updated_filename, updated_file_data)

        with service_mocker("file-manager-service") as file_manager_mocker, mock_source_service(account_id):
            file_info = [
                FileInfo(
                    origin_id=original_doc.file_origin_id,
                    domain="",
                    downloadable_link="",
                    document_type=DocumentType.DESIGN_DOC,
                    id=0,
                    timestamp=None,
                    source_id=None,
                )
            ]
            res_with_files = PaginationResponseFileInfo(results=file_info, size=1, limit=1, start=0)
            file_manager_mocker.put(
                f"/files/{account_id}/info?source-id=-2", MockResponse(res_with_files.model_dump(), status_code=200)
            )
            file_manager_mocker.put(f"/files/{account_id}/zip", MockResponse({}, status_code=200))
            async with mock_job_creation():
                await design_docs_api.update_design_doc_by_file(account_id, original_doc.id, update_file)
        jobs = await service_dal_fixture.scheduler_dal.jobs_dal.get_jobs(account_id)
        assert len(jobs) == 2
        assert jobs[0].cancel_reason == JobCancelReason.FLOW_ALREADY_RUNNING
