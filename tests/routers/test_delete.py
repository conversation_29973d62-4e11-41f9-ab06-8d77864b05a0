import json

import pytest
from prime_security_review_service_client import (
    DesignDocsApi,
)
from prime_security_review_service_client.exceptions import NotFoundException

from service.db import ServiceDAL
from service.errors import DesignDocNotFoundError
from tests.db.test_design_docs_dal import create_design_docs_for_testing
from tests.mock_utils import (
    mock_cases_data,
)


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestDeleteSecurityReview:
    async def test_delete_design_doc(
        self, design_docs_api: DesignDocsApi, service_dal_fixture: ServiceDAL, account_id: str
    ) -> None:
        expected_design_docs = await create_design_docs_for_testing(service_dal_fixture, account_id)
        case_ids = [design_doc.case_id for design_doc in expected_design_docs if design_doc.case_id is not None]
        with mock_cases_data(account_id, case_ids):
            total_docs = len(expected_design_docs)
            for doc in expected_design_docs:
                await design_docs_api.delete_design_doc(account_id, doc.id)
                total_docs -= 1
                design_docs = await design_docs_api.get_design_docs(account_id)
                assert len(design_docs.results) == total_docs
                with pytest.raises(NotFoundException) as e:
                    await design_docs_api.get_design_doc_by_id(account_id, doc.id)
                assert json.loads(e.value.body)["exception"] == repr(DesignDocNotFoundError(doc.id))
