import json

import pytest
from prime_file_manager_service_client import Document<PERSON>ype, FileInfo, PaginationResponseFileInfo
from prime_jobs import JobCancelReason, JobStatus, UpdateJob
from prime_security_review_service_client import (
    BodyReprocessDesignDoc,
    DesignDocsApi,
    JobsApi,
)
from prime_security_review_service_client.exceptions import BadRequestException, NotFoundException
from prime_tests import MockResponse, service_mocker

from service.db import ServiceDA<PERSON>
from service.errors import DesignDocNotFoundError
from service.errors.errors import DesignDocUpdateNotAllowed
from tests.db.test_design_docs_dal import create_design_docs_for_testing
from tests.mock_utils import (
    mock_source_service,
)
from tests.utils import get_job_and_validate_job_creation

from ..jobs.job_test_utils import mock_job_creation


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestReprocess:
    async def test_update_design_doc_by_file(
        self, jobs_api: JobsApi, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    ):
        original_file_data = b"Original PDF content"
        original_filename = "original.pdf"
        design_doc_file = [(original_filename, original_file_data)]

        with service_mocker("file-manager-service") as file_manager_mocker, mock_source_service(account_id):
            res = PaginationResponseFileInfo(results=[], size=1, limit=1, start=0)
            file_manager_mocker.put(
                f"/files/{account_id}/info?source-id=-2", MockResponse(res.model_dump(), status_code=200)
            )
            file_manager_mocker.put(f"/files/{account_id}/zip", MockResponse({}, status_code=200))
            async with mock_job_creation():
                await design_docs_api.design_docs_by_file(
                    account_id, created_by="test", design_doc_files=design_doc_file
                )

        # Get the created design doc
        design_docs = await service_dal_fixture.design_docs_dal.get_design_docs(account_id)
        assert len(design_docs) == 1
        original_doc = design_docs[0]
        original_doc_id = original_doc.id
        original_title = original_doc.title
        original_origin_id = original_doc.file_origin_id

        # Add some analysis results to simulate processed doc
        await service_dal_fixture.design_docs_dal.update_design_doc_fields(
            original_doc_id,
            account_id,
            summary="Original summary",
            mermaid_diagram="Original diagram",
        )
        # Mark the job as completed to allow the update to create a new job
        jobs = await service_dal_fixture.scheduler_dal.jobs_dal.get_jobs(account_id=account_id)
        assert len(jobs) == 1
        await service_dal_fixture.scheduler_dal.jobs_dal.update_job(
            account_id=account_id, job_id=jobs[0].id, update_job=UpdateJob(status=JobStatus.COMPLETED)
        )

        # Now update with a new file
        updated_file_data = b"Updated PDF content"
        updated_filename = "updated.pdf"
        update_file = (updated_filename, updated_file_data)

        with service_mocker("file-manager-service") as file_manager_mocker, mock_source_service(account_id):
            # Mock file exists for update (allow_update=True should handle this)
            file_info = [
                FileInfo(
                    origin_id=str(original_origin_id) if original_origin_id else "",
                    domain="",
                    downloadable_link="",
                    document_type=DocumentType.DESIGN_DOC,
                    id=0,
                    timestamp=None,
                    source_id=None,
                )
            ]
            res_with_files = PaginationResponseFileInfo(results=file_info, size=1, limit=1, start=0)
            file_manager_mocker.put(
                f"/files/{account_id}/info?source-id=-2", MockResponse(res_with_files.model_dump(), status_code=200)
            )
            file_manager_mocker.put(f"/files/{account_id}/zip", MockResponse({}, status_code=200))
            async with mock_job_creation() as deploy_helm_mock:
                response = await design_docs_api.update_design_doc_by_file(account_id, original_doc_id, update_file)

        # Verify the response
        assert response.files_uploaded == 1
        assert response.file_names == [original_title]  # Should preserve original title

        # Verify the doc was updated in the database
        service_dal_fixture.session.expire_all()
        updated_doc = await service_dal_fixture.design_docs_dal.get_design_docs_by_id(account_id, original_doc_id)
        assert updated_doc.id == original_doc_id  # Same ID
        assert updated_doc.title == original_title  # Title preserved
        assert updated_doc.file_origin_id == original_origin_id  # Same origin ID

        # Verify analysis results were cleared
        assert updated_doc.summary is None
        assert updated_doc.mermaid_diagram is None
        assert updated_doc.top_recommendations is None
        assert updated_doc.policy_recommendations is None
        assert updated_doc.attack_scenarios is None
        assert updated_doc.attack_scenario_dataflow_diagram is None

        # Verify job was created for reprocessing
        await get_job_and_validate_job_creation(account_id, [updated_doc], deploy_helm_mock, jobs_api)

        # Verify we still have only one design doc (updated, not created new)
        all_docs = await service_dal_fixture.design_docs_dal.get_design_docs(account_id)
        assert len(all_docs) == 1

    @pytest.mark.usefixtures("service_dal_fixture")
    async def test_update_design_doc_by_file_nonexistent_doc(self, design_docs_api: DesignDocsApi, account_id: str):
        updated_file_data = b"Updated PDF content"
        updated_filename = "updated.pdf"
        update_file = (updated_filename, updated_file_data)

        not_existing_doc_id = 99999
        with pytest.raises(NotFoundException) as e:
            await design_docs_api.update_design_doc_by_file(account_id, not_existing_doc_id, update_file)
        assert json.loads(e.value.body)["exception"] == repr(DesignDocNotFoundError(not_existing_doc_id))

    async def test_update_design_doc_by_file_url_based_doc_error(
        self, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    ):
        url = "https://example.com/doc"
        design_doc = await service_dal_fixture.design_docs_dal.add_design_doc(
            account_id, "URL Doc", "test_user", url=url
        )

        updated_file_data = b"Updated PDF content"
        updated_filename = "updated.pdf"
        update_file = (updated_filename, updated_file_data)

        with pytest.raises(BadRequestException) as exc_info:
            await design_docs_api.update_design_doc_by_file(account_id, design_doc.id, update_file)

        assert json.loads(exc_info.value.body)["title"] == DesignDocUpdateNotAllowed.__name__

    async def test_update_design_doc_by_file_error_starting_job(
        self, jobs_api: JobsApi, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    ):
        original_file_data = b"Original PDF content"
        original_filename = "original.pdf"
        design_doc_file = [(original_filename, original_file_data)]

        with service_mocker("file-manager-service") as file_manager_mocker, mock_source_service(account_id):
            res = PaginationResponseFileInfo(results=[], size=1, limit=1, start=0)
            file_manager_mocker.put(
                f"/files/{account_id}/info?source-id=-2", MockResponse(res.model_dump(), status_code=200)
            )
            file_manager_mocker.put(f"/files/{account_id}/zip", MockResponse({}, status_code=200))
            async with mock_job_creation():
                await design_docs_api.design_docs_by_file(
                    account_id, created_by="test", design_doc_files=design_doc_file
                )

        # # Get the created design doc
        design_docs = await service_dal_fixture.design_docs_dal.get_design_docs(account_id)
        original_doc = design_docs[0]

        # Now update with a new file
        updated_file_data = b"Updated PDF content"
        updated_filename = "updated.pdf"
        update_file = (updated_filename, updated_file_data)

        with service_mocker("file-manager-service") as file_manager_mocker, mock_source_service(account_id):
            file_info = [
                FileInfo(
                    origin_id=original_doc.file_origin_id,
                    domain="",
                    downloadable_link="",
                    document_type=DocumentType.DESIGN_DOC,
                    id=0,
                    timestamp=None,
                    source_id=None,
                )
            ]
            res_with_files = PaginationResponseFileInfo(results=file_info, size=1, limit=1, start=0)
            file_manager_mocker.put(
                f"/files/{account_id}/info?source-id=-2", MockResponse(res_with_files.model_dump(), status_code=200)
            )
            file_manager_mocker.put(f"/files/{account_id}/zip", MockResponse({}, status_code=200))
            async with mock_job_creation():
                await design_docs_api.update_design_doc_by_file(account_id, original_doc.id, update_file)
        jobs = await service_dal_fixture.scheduler_dal.jobs_dal.get_jobs(account_id)
        assert len(jobs) == 2
        assert jobs[0].cancel_reason == JobCancelReason.FLOW_ALREADY_RUNNING

    async def test_reprocess_design_doc(
        self, jobs_api: JobsApi, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    ):
        expected_design_docs = await create_design_docs_for_testing(service_dal_fixture, account_id)
        doc_to_reprocess = expected_design_docs[0]
        with service_mocker("file-manager-service"), mock_source_service(account_id):
            async with mock_job_creation() as deploy_helm_mock:
                await design_docs_api.reprocess_design_doc(
                    account_id,
                    doc_to_reprocess.id,
                    BodyReprocessDesignDoc(created_by="test_user_id"),
                )

            await get_job_and_validate_job_creation(account_id, [doc_to_reprocess], deploy_helm_mock, jobs_api)

            design_docs = await service_dal_fixture.design_docs_dal.get_design_docs(account_id)
            assert len(expected_design_docs) == len(design_docs)
