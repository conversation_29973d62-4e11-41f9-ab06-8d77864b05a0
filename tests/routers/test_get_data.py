import random
from datetime import UTC, datetime

from prime_security_review_service_client.models.body_create_design_doc_container import BodyCreateDesignDocContainer
import pytest
from prime_jobs import JobStatus
from prime_redis_utils import AsyncPrefixRedisClient
from prime_security_review_service_client import (
    DesignDocProcessStatus,
    DesignDocResponse,
    DesignDocsApi,
)
from prime_tests import MockResponse, service_mocker

from service.db import DesignDocsTable, ServiceDAL
from service.jobs.design_docs_job.utils import get_redis_processing_progress_percent_key
from service.models.design_docs import DesignDocType
from tests.db.test_design_docs_dal import create_design_docs_for_testing
from tests.jobs.job_test_utils import mock_job_creation
from tests.mock_utils import (
    DESIGN_DOCS_SOURCE_ID,
    _basic_mocks,
    get_external_cases,
    mock_cases_data,
    mock_source_service,
)


async def _set_processing_progress_percent(account_redis_client, design_docs, progress_mapping):
    for doc in design_docs:
        await account_redis_client.set(
            get_redis_processing_progress_percent_key(doc.id),
            DesignDocProcessStatus(
                processing_progress_percent=progress_mapping[doc.id],
                job_status=JobStatus.RUNNING,
                timestamp=datetime.now(UTC),
            ).model_dump_json(),
        )


async def _validate_db_and_response(
    design_doc_db: DesignDocsTable,
    design_doc_response: DesignDocResponse,
    processing_status: DesignDocProcessStatus | None = None,
) -> None:
    assert design_doc_db.title == design_doc_response.title
    assert design_doc_db.file_origin_id == design_doc_response.file_origin_id
    assert design_doc_db.updated_at == design_doc_response.updated_at
    assert design_doc_response.url == design_doc_db.url
    assert design_doc_db.case_id == design_doc_response.case_id
    if processing_status:
        assert design_doc_response.processing_status is not None
        assert design_doc_response.processing_status.job_status == processing_status.job_status
        assert (
            design_doc_response.processing_status.processing_progress_percent
            == processing_status.processing_progress_percent
        )
        assert design_doc_response.processing_status.timestamp is not None


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestGetDesignDocs:
    async def test_get_design_docs(
        self, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    ):
        expected_design_docs = await create_design_docs_for_testing(service_dal_fixture, account_id)

        case_ids = [design_doc.case_id for design_doc in expected_design_docs if design_doc.case_id is not None]
        with mock_cases_data(account_id, case_ids):
            actual_design_docs = await design_docs_api.get_design_docs(account_id)

            result_design_docs = actual_design_docs.results
            created_at_list = [doc.created_at for doc in result_design_docs]
            assert created_at_list == sorted(
                created_at_list, reverse=True
            ), "Design docs are not in descending order by created_at"

            expected_design_docs.sort(key=lambda d: d.created_at, reverse=True)
            for expected, actual in zip(expected_design_docs, result_design_docs, strict=False):
                await _validate_db_and_response(expected, actual)

    async def test_get_design_docs_processing_progress(
        self,
        service_dal_fixture: ServiceDAL,
        design_docs_api: DesignDocsApi,
        account_id: str,
        account_redis_client: AsyncPrefixRedisClient,
    ):
        expected_design_docs = await create_design_docs_for_testing(service_dal_fixture, account_id)

        processing_progress_percent_mapping = {
            expected_design_doc.id: random.uniform(0, 100) for expected_design_doc in expected_design_docs
        }
        await _set_processing_progress_percent(
            account_redis_client, expected_design_docs, processing_progress_percent_mapping
        )

        case_ids = [design_doc.case_id for design_doc in expected_design_docs if design_doc.case_id is not None]
        with mock_cases_data(account_id, case_ids):
            actual_design_docs = await design_docs_api.get_design_docs(account_id)

            expected_design_docs.sort(key=lambda d: d.created_at, reverse=True)
            for expected, actual in zip(expected_design_docs, actual_design_docs.results, strict=False):
                await _validate_db_and_response(
                    expected,
                    actual,
                    DesignDocProcessStatus(
                        processing_progress_percent=processing_progress_percent_mapping[expected.id],
                        job_status=JobStatus.RUNNING,
                        timestamp=datetime.now(UTC),
                    ),
                )

    async def test_get_design_docs_with_pagination(
        self, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    ):
        expected_design_docs = await create_design_docs_for_testing(service_dal_fixture, account_id)
        expected_design_docs.sort(key=lambda d: d.created_at, reverse=True)

        case_ids = [design_doc.case_id for design_doc in expected_design_docs if design_doc.case_id is not None]
        with mock_cases_data(account_id, case_ids):
            actual_design_docs = (await design_docs_api.get_design_docs(account_id, limit=3, offset=2)).results
            for expected, actual in zip(expected_design_docs[2 : 2 + 3], actual_design_docs, strict=False):
                await _validate_db_and_response(expected, actual)

            design_docs = await design_docs_api.get_design_docs(account_id, limit=50, offset=2)
            assert len(design_docs.results) == len(expected_design_docs) - 2
            assert design_docs.total == len(expected_design_docs)

    async def test_get_design_doc_by_id(
        self, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    ) -> None:
        expected_design_docs = await create_design_docs_for_testing(service_dal_fixture, account_id)
        case_ids = [design_doc.case_id for design_doc in expected_design_docs if design_doc.case_id is not None]
        with mock_cases_data(account_id, case_ids):
            for design_doc_db in expected_design_docs:
                extended_design_doc = await design_docs_api.get_design_doc_by_id(account_id, design_doc_db.id)
                await _validate_db_and_response(design_doc_db, extended_design_doc)
                assert design_doc_db.summary or extended_design_doc.summary == ""
                assert design_doc_db.mermaid_diagram == extended_design_doc.mermaid_diagram
                assert design_doc_db.top_recommendations or extended_design_doc.top_recommendations == []
                assert design_doc_db.policy_recommendations or extended_design_doc.policy_recommendations == []
                assert design_doc_db.attack_scenarios or extended_design_doc.attack_scenarios == []

    async def test_download_design_doc(
        self, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    ) -> None:
        file_binary_data = b"binary_data"
        with _basic_mocks() as (file_manager_mocker, _, _):
            expected_design_docs = await create_design_docs_for_testing(service_dal_fixture, account_id)
            for design_docs_db in expected_design_docs:
                design_doc = await service_dal_fixture.design_docs_dal.get_design_docs_by_id(
                    account_id, design_docs_db.id
                )
                if design_doc.file_origin_id is None:
                    continue

                file_manager_mocker.get(
                    f"/files/{account_id}/data/source/{DESIGN_DOCS_SOURCE_ID}/origin_id/{design_doc.file_origin_id}",
                    MockResponse(content=file_binary_data),
                )
                data = await design_docs_api.download_design_doc(account_id, design_doc.id)
                assert data == file_binary_data


    # async def test_design_docs_by_container(
    #     self, jobs_api: JobsApi, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    # ) -> None:
    #     case = get_external_cases(account_id, [1])[0]
    #     with (
    #         mock_source_service(account_id),
    #         service_mocker("rat-logic-service") as rat_logic_mocker,
    #     ):
    #         rat_logic_mocker.get(f"/cases/{account_id}/case/{case.case_id}", MockResponse(case.model_dump()))
    #         async with mock_job_creation():
    #             body = BodyCreateDesignDocContainer(created_by="test_user_id")
    #             await design_docs_api.create_design_doc_container(account_id, case.case_id, body)
    #         design_doc = await service_dal_fixture.design_docs_dal.get_design_doc(account_id, case_id=case.case_id)
    #         assert design_doc.title == case.title
    #         assert design_doc.case_id == case.case_id

    # async def test_get_security_review_by_container_success(
    #     self, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    # ) -> None:
    #     """Test successful retrieval of security review by container"""
    #     case = get_external_cases(account_id, [1])[0]

    #     # First create a design doc container directly in the database
    #     design_doc = await service_dal_fixture.design_docs_dal.add_design_doc(
    #         account_id=account_id,
    #         title=case.title,
    #         created_by="test_user_id",
    #         case_id=case.case_id,
    #         doc_source_type=DesignDocType.CONTAINER,
    #     )

    #     # Mock the cases API call for the GET endpoint
    #     with mock_cases_data(account_id, [case.case_id]):
    #         # Test the endpoint using the generated client
    #         response = await design_docs_api.get_security_review_by_container(
    #             account_id=account_id,
    #             case_id=case.case_id
    #         )

    #         # Verify response structure and content
    #         assert response.title == case.title
    #         assert response.case_id == case.case_id
    #         assert response.created_by == "test_user_id"
    #         assert response.doc_source_type == DesignDocType.CONTAINER
    #         assert response.id == design_doc.id
    #         assert response.created_at is not None
    #         assert response.updated_at is not None    

    # @pytest.mark.usefixtures("service_dal_fixture")
    # async def test_get_security_review_by_container_not_found(
    #     self, design_docs_api: DesignDocsApi, account_id: str
    # ) -> None:
    #     """Test 404 response when security review doesn't exist for container"""
    #     non_existent_case_id = 99999

    #     # Test the endpoint using the generated client and expect it to raise NotFoundException
    #     from prime_security_review_service_client.exceptions import NotFoundException
    #     from service.errors import DesignDocNotFoundError
    #     import pytest
    #     import json

    #     # Call the endpoint and expect it to raise NotFoundException (404)
    #     with pytest.raises(NotFoundException) as e:
    #         await design_docs_api.get_security_review_by_container(
    #             account_id=account_id,
    #             case_id=non_existent_case_id
    #         )

    #     # Verify the exception is a 404 and contains the expected error
    #     assert json.loads(e.value.body)["exception"] == repr(DesignDocNotFoundError(non_existent_case_id))

    # async def test_get_security_review_by_container_with_case_data(
    #     self, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    # ) -> None:
    #     """Test successful retrieval of security review by container with case data"""
    #     case = get_external_cases(account_id, [1])[0]

    #     # Create a design doc container directly in the database
    #     design_doc = await service_dal_fixture.design_docs_dal.add_design_doc(
    #         account_id=account_id,
    #         title=case.title,
    #         created_by="test_user_id",
    #         case_id=case.case_id,
    #         doc_source_type=DesignDocType.CONTAINER,
    #     )

    #     # Mock the cases API call for the GET endpoint
    #     with mock_cases_data(account_id, [case.case_id]):
    #         # Test the endpoint using the generated client
    #         response = await design_docs_api.get_security_review_by_container(
    #             account_id=account_id,
    #             case_id=case.case_id
    #         )

    #         # Verify response structure and content
    #         assert response.title == case.title
    #         assert response.case_id == case.case_id
    #         assert response.created_by == "test_user_id"
    #         assert response.doc_source_type == DesignDocType.CONTAINER
    #         assert response.id == design_doc.id
    #         assert response.created_at is not None
    #         assert response.updated_at is not None
    #         # Verify case data is included
    #         assert response.issue_id == case.issue_id
    #         assert response.source_id == case.source_id
    #         assert response.jira_issue_url == case.link