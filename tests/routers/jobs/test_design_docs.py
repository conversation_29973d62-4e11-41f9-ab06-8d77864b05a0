import pytest
from prime_jobs import <PERSON><PERSON>tat<PERSON>
from prime_rat_logic_service_client import Jobs<PERSON><PERSON>
from prime_security_review_service_client import JobDesignDocsCreateArgs

from service.db import ServiceDAL
from service.job_type import JobType
from service.jobs.design_docs_job.models import DesignDocsJobArgs
from tests.fixtures import TEST_CREATED_BY
from tests.jobs.job_test_utils import generic_job_validation, get_next_job_id


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestDesignDocsJob:
    async def test_design_docs_job_run(self, jobs_api: JobsApi, service_dal_fixture: ServiceDAL, account_id: str):
        create_arg = JobDesignDocsCreateArgs(
            job=JobType.DESIGN_DOCS,
            created_by=TEST_CREATED_BY,
            force=False,
        )
        job_id = await get_next_job_id(service_dal_fixture)
        expected_job_args = DesignDocsJobArgs(account_id=account_id, job_id=job_id, force=False, design_doc_ids=None)
        job = await generic_job_validation(account_id, jobs_api, JobType.DESIGN_DOCS, create_arg, expected_job_args)
        old_job = await service_dal_fixture.scheduler_dal.jobs_dal.get_job_by_id(account_id, job.job_id)
        old_job.status = JobStatus.COMPLETED
        await service_dal_fixture.session.commit()

        create_arg = JobDesignDocsCreateArgs(
            job=JobType.DESIGN_DOCS,
            created_by=TEST_CREATED_BY,
            force=False,
        )
        expected_job_args = DesignDocsJobArgs(account_id=account_id, job_id=job_id + 1, force=False)
        await generic_job_validation(account_id, jobs_api, JobType.DESIGN_DOCS, create_arg, expected_job_args)
