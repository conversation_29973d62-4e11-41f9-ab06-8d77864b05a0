import pytest
from prime_security_review_service_client import (
    BodyCreateDesignDocContainer,
    DesignDocsApi,
    JobsApi,
)
from prime_tests import Mo<PERSON><PERSON><PERSON>po<PERSON>, service_mocker

from service.db import ServiceDAL
from tests.mock_utils import (
    get_external_cases,
    mock_source_service,
)

from ..jobs.job_test_utils import mock_job_creation


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestCreationByContainer:
    async def test_create_design_docs_by_container(
        self, jobs_api: JobsApi, service_dal_fixture: ServiceDAL, design_docs_api: DesignDocsApi, account_id: str
    ) -> None:
        case = get_external_cases(account_id, [1])[0]
        with (
            mock_source_service(account_id),
            service_mocker("rat-logic-service") as rat_logic_mocker,
        ):
            rat_logic_mocker.get(f"/cases/{account_id}/case/{case.case_id}", MockResponse(case.model_dump()))
            async with mock_job_creation():
                body = BodyCreateDesignDocContainer(created_by="test_user_id")
                await design_docs_api.create_design_doc_container(account_id, case.case_id, body)
            design_doc = await service_dal_fixture.design_docs_dal.get_design_doc(account_id, case_id=case.case_id)
            assert design_doc.title == case.title
            assert design_doc.case_id == case.case_id
