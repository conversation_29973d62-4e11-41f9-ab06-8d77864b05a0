import pytest
from prime_security_review_service_client import (
    AttachContextToDesignDocReq,
    ContextType,
    DesignDocsApi,
    JobsApi,
)
from prime_security_review_service_client.exceptions import BadRequestException
from prime_tests import service_mocker

from service.db import ServiceDAL
from tests.db.test_design_docs_dal import create_design_docs_for_testing
from tests.mock_utils import (
    _agent_api_mocks,
    mock_source_service,
)
from tests.utils import get_job_and_validate_job_creation

from ..jobs.job_test_utils import mock_job_creation


class TestAttachContext:
    async def test_attach_context_to_design_doc(
        self, design_docs_api: DesignDocsApi, service_dal_fixture: ServiceDAL, account_id: str, jobs_api: JobsApi
    ):
        conversation_id_to_attach = 1
        expected_design_docs = await create_design_docs_for_testing(service_dal_fixture, account_id)
        doc_to_attach_context = expected_design_docs[0]
        assert doc_to_attach_context.agent_conversation_id is None
        with (
            _agent_api_mocks(conv_id=conversation_id_to_attach),
            service_mocker("file-manager-service"),
            mock_source_service(account_id),
        ):
            async with mock_job_creation() as deploy_helm_mock:
                await design_docs_api.attach_context_to_design_doc(
                    account_id,
                    doc_to_attach_context.id,
                    AttachContextToDesignDocReq(
                        created_by="test_user_id", context_type=ContextType.AGENT, context_id=conversation_id_to_attach
                    ),
                )
                actual_doc = await service_dal_fixture.design_docs_dal.get_design_docs_by_id(
                    account_id, doc_to_attach_context.id
                )
                assert actual_doc.agent_conversation_id == conversation_id_to_attach
                await get_job_and_validate_job_creation(account_id, [actual_doc], deploy_helm_mock, jobs_api)
                with pytest.raises(BadRequestException) as exc_info:
                    await design_docs_api.attach_context_to_design_doc(
                        account_id,
                        doc_to_attach_context.id,
                        AttachContextToDesignDocReq(
                            created_by="test_user_id",
                            context_type=ContextType.AGENT,
                            context_id=conversation_id_to_attach + 1,
                        ),
                    )
            assert exc_info.value.status == 400
