import pytest
from prime_security_review_service_client import (
    DesignDocsApi,
    JobsApi,
)
from prime_tests import create_async_test_service_client
from prime_tests.fastapi_test_client import AsyncAppWrapper

from service.service_app import app


@pytest.fixture(scope="session")
def app_wrapper():
    return AsyncAppWrapper(app)


@pytest.fixture(scope="session")
async def jobs_api(app_wrapper) -> JobsApi:
    return create_async_test_service_client(JobsApi, app_wrapper)


@pytest.fixture(scope="session")
async def design_docs_api(app_wrapper) -> DesignDocsApi:
    return create_async_test_service_client(DesignDocsApi, app_wrapper)
