import asyncio
import logging
import os
import random
import subprocess
from collections.abc import As<PERSON><PERSON>enerator

import pytest
from prime_celery import PrimeCelery
from prime_celery.app import VHOST
from prime_jobs import SchedulerDAL
from prime_redis_utils import AsyncPrefixRedisClient, AsyncRedisClient
from prime_tests import fake_redis_fixture  # noqa: F401
from sqlalchemy.ext.asyncio import AsyncSession

from service.config import get_config
from service.db import ServiceDAL
from service.services_clients import close_clients
from tests.mock_utils import ACCOUNT_ID_CONTEXT, SOURCE_ID

TEST_CREATED_BY = "test_created_by"

os.environ["RECREATION_CLEANUP"] = "False"
os.environ["DISPOSE_ENGINE"] = "True"
os.environ["CLOSE_CONNECTIONS"] = "True"
# os.environ["SESSION_LEVEL"] = "True"

worker_id = os.environ.get("PYTEST_XDIST_WORKER", "gw0")
os.environ["DB_NAME"] = f"{os.environ['db_name']}_{worker_id}"  # noqa: SIM112

LOGGER = logging.getLogger("test")


@pytest.fixture(scope="function", autouse=True)
async def function_wrapper():
    yield
    LOGGER.info("Current event loop %s", id(asyncio.get_event_loop()))


@pytest.fixture(scope="session", autouse=True)
async def session_wrapper():
    try:
        yield
    finally:
        await close_clients()


@pytest.fixture
async def service_dal_fixture(async_db_fixture: AsyncSession) -> AsyncGenerator[ServiceDAL]:
    yield ServiceDAL(async_db_fixture)


@pytest.fixture
async def scheduler_dal_fixture(async_db_fixture: AsyncSession) -> AsyncGenerator[SchedulerDAL]:
    yield SchedulerDAL(async_db_fixture)


@pytest.fixture
def account_id() -> str:
    account_id = f"test_account_id_{random.randint(1, 999999)}"
    ACCOUNT_ID_CONTEXT.set(account_id)
    return account_id


@pytest.fixture
def account_redis_client(fake_redis_fixture: AsyncRedisClient, account_id: str) -> AsyncPrefixRedisClient:
    return AsyncPrefixRedisClient(prefix=account_id, redis=fake_redis_fixture)


@pytest.fixture(scope="session")
def celery_config():
    app = PrimeCelery("test", get_config(), get_config().celery_redis)
    return {
        "broker_url": app._get_broker_url,
        "result_backend": app._get_backend_url,
        "worker_prefetch_multiplier": 1,
        "worker_concurrency": 1,
    }


@pytest.fixture(scope="session")
def rabbit():
    conf = get_config()
    cmd = [
        "curl",
        "-u",
        f"{conf.rabbitmq_username}:{conf.rabbitmq_password}",
        "-X",
        "PUT",
        f"http://{conf.rabbitmq_hostname}:15672/api/vhosts/{VHOST}",
    ]
    subprocess.check_call(cmd)


@pytest.fixture
def celery_worker_parameters(account_id: str):
    return {
        "queues": (f"ai_results__{account_id}_{SOURCE_ID}"),
        "perform_ping_check": False,  # Skip ping check if still having issues
    }


@pytest.fixture(scope="session")
def celery_worker_pool(rabbit):
    return "threads"
