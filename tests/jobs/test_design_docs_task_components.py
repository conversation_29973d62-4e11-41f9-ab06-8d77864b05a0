import pytest
from prime_gen_ai_service_client import (
    ResearchFlowsAttackScenariosFlowPromptsRecommendationsPromptQuotes,
    TopRecommendation,
)

from service.jobs.design_docs_job.utils import to_design_doc_recommendations
from service.models import (
    DesignDocTopRecommendation,
)


@pytest.fixture
def test_recommendations() -> list[TopRecommendation]:
    quotes = ["Test quote1", "Test quote2", "Test quote3"]

    result = [
        TopRecommendation(
            id=1,
            title="Test Title1",
            description="Test Description1",
            concern="Concern1",
            explanation="explanation1",
            evidence="Test Evidence1",
            source_information="source_information1",
            control_status="control_status1",
            quote_list=ResearchFlowsAttackScenariosFlowPromptsRecommendationsPromptQuotes(quote=[]),
            ids="1,2",
            component="component1",
            references=[1, 2],
        )
    ]

    result.extend(
        [
            TopRecommendation(
                id=1,
                title="Test Title2",
                description="Test Description2",
                concern="Concern2",
                explanation="explanation2",
                quote_list=ResearchFlowsAttackScenariosFlowPromptsRecommendationsPromptQuotes(quote=quotes),
                evidence="Test Evidence2",
                source_information="source_information2",
                control_status="control_status2",
                ids="1,2,3",
                component="component2",
                references=[1, 2, 3],
            )
        ]
    )

    return result


class TestToDesignRecommendation:
    def test_to_design_recommendation_with_empty_recommendations(self) -> None:
        result = to_design_doc_recommendations([])
        assert result == []

    def test_to_design_recommendation_with_recommendations(self, test_recommendations: list[TopRecommendation]) -> None:
        result = to_design_doc_recommendations(test_recommendations)
        assert len(result) == 2
        assert isinstance(result[0], DesignDocTopRecommendation)
        assert result[0].id == 1
        assert result[0].title == "Test Title1"
        assert result[0].description == "Test Description1"
        assert result[0].references == [1, 2]
        assert result[0].component == "component1"
        assert result[0].concern == "Concern1"
        assert result[0].evidence == "Test Evidence1"
        assert result[0].quotes is not None
        assert len(result[0].quotes) == 0

        assert result[1].id == 1
        assert result[1].title == "Test Title2"
        assert result[1].description == "Test Description2"
        assert result[1].references == [1, 2, 3]
        assert result[1].component == "component2"
        assert result[1].concern == "Concern2"
        assert result[1].evidence == "Test Evidence2"
        assert result[1].quotes
        assert isinstance(result[1].quotes[1], str)
        assert result[1].quotes == ["Test quote1", "Test quote2", "Test quote3"]

    def test_to_design_recommendation_with_empty_quotes(self, test_recommendations: list[TopRecommendation]) -> None:
        test_recommendations[0].quote_list.quote = []
        result = to_design_doc_recommendations(test_recommendations)
        assert len(result) == 2
        assert result[0].quotes is not None
        assert len(result[0].quotes) == 0

    def test_to_design_recommendation_with_none_quotes(self, test_recommendations: list[TopRecommendation]) -> None:
        test_recommendations[0].quote_list.quote = None
        result = to_design_doc_recommendations(test_recommendations)
        assert len(result) == 2
        assert result[0].quotes is not None
        assert len(result[0].quotes) == 0
