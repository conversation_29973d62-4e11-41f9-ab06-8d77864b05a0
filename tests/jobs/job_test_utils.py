from collections.abc import Generator
from contextlib import asynccontextmanager, contextmanager
from typing import Any
from unittest.mock import patch

from kubernetes.client import V1Job, V1ObjectMeta
from packaging.version import Version
from prime_jobs import Job, JobConfig, JobStatus, ServiceInfo
from prime_security_review_service_client import JobDesignDocsCreateArgs, JobsA<PERSON>
from prime_utils.helm_and_kubectl import deploy_helm
from sqlmodel import select

from service.config import get_config
from service.db import ServiceDAL
from service.job_type import JobType
from service.jobs.job_spawners import (
    SecurityReviewJobInfo,
    SecurityReviewJobSpawnerBase,
    SecurityReviewJobSpawnerFactory,
    job_info_types,
)
from service.models.jobs import JobCreatedResponse

CELERY_MANAGER_RESULT_TARGET = (
    "service.jobs.base_job_logic.gen_ai_celery.celery_manager.CeleryManager.send_and_wait_for_result"
)


@contextmanager
def mock_deploy_helm(job_name: str | None = None):
    job_name = job_name or "job_name"
    ret_value = [V1Job(api_version="batch/v1", kind="Job", metadata=V1ObjectMeta(name=job_name, namespace="default"))]
    with patch("prime_jobs.job_spawner.deploy_helm") as deploy_helm_mock:
        deploy_helm_mock.return_value = ret_value
        yield deploy_helm_mock


# TODO: can we remove the need for passing the spawner object?
@asynccontextmanager
async def mock_job_creation():
    with mock_deploy_helm() as deploy_helm_mock:
        yield deploy_helm_mock


async def _get_job_args(spawner_obj: SecurityReviewJobSpawnerBase, expected_job_args: job_info_types):
    job_id = expected_job_args.job_id
    job_name = spawner_obj.get_job_name(job_id)
    job_type = f"{spawner_obj.NAME}-job"
    expected_job_config = JobConfig(
        job_env=spawner_obj._convert_to_helm_job_env(expected_job_args),
        job_info=SecurityReviewJobInfo(
            image=get_config().jobs_image_url,
            name=job_name,
            id=job_id,
            job_type=job_type,
            module_path=f"service.jobs.{job_type.replace('-', '_')}.main",
        ),
        service=ServiceInfo(name=get_config().service_name),
        datadog_enabled=get_config().datadog_enabled,
    )
    return job_name, expected_job_config


async def validate_job_creation(
    spawner: SecurityReviewJobSpawnerBase, expected_job_args: job_info_types, deploy_helm_mock
):
    job_name, expected_job_config = await _get_job_args(spawner, expected_job_args)
    deploy_helm_mock.assert_called_with(
        job_name, spawner.job_path(), expected_job_config.model_dump(mode="json", serialize_as_any=True)
    )


@contextmanager
def generic_mock_celery_manager(responses: dict[str, dict[str, Any]]) -> Generator[None]:
    messages: list[Any] = []

    def _side_effect(*args, **kwargs: dict[str, Any]) -> dict[str, Any]:
        task_name = kwargs["task_name"]
        messages.append((args, kwargs))
        return responses.get(task_name, {})

    with patch(CELERY_MANAGER_RESULT_TARGET, side_effect=_side_effect):
        yield messages


@contextmanager
def mock_version(gen_ai_version: str = "1.0.0") -> Generator[None]:
    gen_ai_version_target = "service.jobs.base_job_logic.gen_ai_base_job.GenAIBaseJob.get_ai_version"
    with patch(gen_ai_version_target) as base_mock_get_celery_app:
        base_mock_get_celery_app.return_value = Version(gen_ai_version)
        yield


async def generic_job_validation(
    account_id: str,
    jobs_api: JobsApi,
    job_type: JobType,
    create_arg: JobDesignDocsCreateArgs,
    expected_job_args: JobDesignDocsCreateArgs,
) -> JobCreatedResponse:
    spawner = SecurityReviewJobSpawnerFactory.JOB_SPAWNERS[job_type]
    spawner_args = create_arg.to_dict()
    spawner_args.pop("job")
    spawner_args.pop("created_by")
    spawner_obj = spawner(account_id=account_id, **spawner_args)
    async with mock_job_creation() as deploy_helm_mock:
        resp = await jobs_api.add_job(
            account_id=account_id,
            job_design_docs_create_args=JobDesignDocsCreateArgs(
                design_doc_ids=create_arg.design_doc_ids,
                job=create_arg.job,
                created_by=create_arg.created_by,
                force=create_arg.force,
            ),
        )
    await validate_job_creation(spawner_obj, expected_job_args, deploy_helm_mock)
    assert resp.job_id
    assert resp.status == JobStatus.PENDING
    # make sure the template is ok
    job_name, expected_job_config = await _get_job_args(spawner_obj, expected_job_args)
    with patch("prime_utils.helm_and_kubectl.run_kubectl_apply"):
        deploy_helm(job_name, spawner.job_path(), expected_job_config.model_dump(mode="json", serialize_as_any=True))
    return resp


async def get_next_job_id(service_dal: ServiceDAL) -> int:
    last_job = (await service_dal.session.exec(select(Job).order_by(Job.id.desc()).limit(1))).one_or_none()
    return last_job.id + 1 if last_job else 1
