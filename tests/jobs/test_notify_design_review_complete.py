from unittest.mock import Async<PERSON>ock, MagicMock, patch

import pytest
from prime_events import <PERSON><PERSON>otifier
from prime_notification_service_client import MailNotifyReq, NotifyResponse, OutputNotificationType
from prime_shared.consts import AUTO_USER
from prime_tests import <PERSON><PERSON><PERSON><PERSON>ponse, service_mocker

from service.jobs.design_docs_job.design_docs_job import DesignDocsJob
from service.jobs.design_docs_job.models import DesignDocProcessorResult
from service.jobs.design_docs_job.notify_design_review_complete import NotifyDesignReviewCompleted
from service.models.design_docs import DesignDocType
from service.services_clients import ServicesClients


@pytest.fixture
def notify_params():
    return {"user": "test_user", "document_id": 123}


@pytest.fixture
def notify_instance(account_id, notify_params):
    return NotifyDesignReviewCompleted(account_id=account_id, **notify_params)


class TestNotifyDesignReview:
    async def test_notify_design_review_completed(self, account_id: str, notify_instance):
        with service_mocker("notification-service") as notification_mocker:
            notification_response = NotifyResponse(sent=True)
            notification_mocker.post(f"/notify/mail/{account_id}", MockResponse(notification_response.to_json()))

            await notify_instance.notify()

            url_path = f"notify/mail/{account_id}"
            assert len(notification_mocker.requests["POST"][url_path]) > 0, "No notification request was made"
            request_data = notification_mocker.requests["POST"][url_path][0]
            assert request_data.args[0] == "POST"
            assert f"/notify/mail/{account_id}" in request_data.args[1]

    async def test_send_mail_notification_spy(self, account_id: str, notify_instance):
        with (
            patch.object(
                NotifyDesignReviewCompleted,
                "_send_mail_notification",
                wraps=NotifyDesignReviewCompleted._send_mail_notification,
            ) as send_mail_spy,
            service_mocker("notification-service") as notification_mocker,
        ):
            notification_response = NotifyResponse(sent=True)
            notification_mocker.post(f"/notify/mail/{account_id}", MockResponse(notification_response.to_json()))

            await notify_instance.notify()

            assert send_mail_spy.called, "The _send_mail_notification method was not called"

    async def test_notify_design_review_completed_exception_handling(self, account_id: str, notify_instance):
        with service_mocker("notification-service") as notification_mocker:
            notification_mocker.post(f"/notify/mail/{account_id}", MockResponse("{}", status_code=500))

            await notify_instance.notify()

            url_path = f"notify/mail/{account_id}"
            assert len(notification_mocker.requests["POST"][url_path]) > 0, "No notification request was made"

    async def test_send_mail_notification_directly(self, account_id: str, notify_params, notify_instance):
        with patch.object(ServicesClients, "notification_api") as mock_notification_api:
            mock_api = AsyncMock()
            mock_notification_api.return_value = mock_api

            await notify_instance._send_mail_notification()

            mock_api.notify_mail.assert_called_once()

            call_args = mock_api.notify_mail.call_args[1]

            assert call_args["account_id"] == account_id

            mail_notify_req = call_args["mail_notify_req"]
            assert isinstance(mail_notify_req, MailNotifyReq)

            actual_instance = mail_notify_req.actual_instance

            assert actual_instance.user == notify_params["user"]
            assert actual_instance.mail_recipients == [notify_params["user"]]
            assert actual_instance.notification_type == OutputNotificationType.DESIGN_REVIEW_COMPLETED.value
            assert actual_instance.document_id == str(notify_params["document_id"])

    async def test_notify_design_doc_flow_completion_with_regular_user(self, account_id: str):
        with (
            patch.object(DesignDocsJob, "_send_user_notification") as mock_send_notification,
            patch.object(EventNotifier, "__init__", return_value=None),
            patch.object(EventNotifier, "notify", new_callable=AsyncMock),
            patch("service.jobs.base_job_logic.base_job_logic.get_redis_for_account", return_value=MagicMock()),
        ):
            job = DesignDocsJob(
                account_id=account_id,
                job_id=123,
                design_doc_ids=None,
                force=False,
                service_dal=AsyncMock(),
                source_id=456,
            )

            job._created_by = "regular_user"

            design_doc_result = DesignDocProcessorResult(
                design_doc_id=789, design_doc_name="test_doc", design_doc_type=DesignDocType.ORIGINAL
            )

            await job._notify_design_doc_flow_completion(True, [design_doc_result])

            mock_send_notification.assert_called_once_with("regular_user", account_id, 789)

    async def test_notify_design_doc_flow_completion_with_auto_user(self, account_id: str):
        with (
            patch.object(DesignDocsJob, "_send_user_notification") as mock_send_notification,
            patch.object(EventNotifier, "__init__", return_value=None),
            patch.object(EventNotifier, "notify", new_callable=AsyncMock),
            patch("service.jobs.base_job_logic.base_job_logic.get_redis_for_account", return_value=MagicMock()),
        ):
            job = DesignDocsJob(
                account_id=account_id,
                job_id=123,
                design_doc_ids=None,
                force=False,
                service_dal=AsyncMock(),
                source_id=456,
            )

            job._created_by = AUTO_USER

            design_doc_result = DesignDocProcessorResult(
                design_doc_id=789, design_doc_name="test_doc", design_doc_type=DesignDocType.ORIGINAL
            )

            await job._notify_design_doc_flow_completion(True, [design_doc_result])

            mock_send_notification.assert_not_called()

    async def test_notify_design_doc_flow_completion_with_multiple_docs(self, account_id: str):
        with (
            patch.object(DesignDocsJob, "_send_user_notification") as mock_send_notification,
            patch.object(EventNotifier, "__init__", return_value=None),
            patch.object(EventNotifier, "notify", new_callable=AsyncMock),
            patch("service.jobs.base_job_logic.base_job_logic.get_redis_for_account", return_value=MagicMock()),
        ):
            job = DesignDocsJob(
                account_id=account_id,
                job_id=123,
                design_doc_ids=None,
                force=False,
                service_dal=AsyncMock(),
                source_id=456,
            )

            job._created_by = "regular_user"

            design_doc_results = [
                DesignDocProcessorResult(
                    design_doc_id=789, design_doc_name="test_doc_1", design_doc_type=DesignDocType.ORIGINAL
                ),
                DesignDocProcessorResult(
                    design_doc_id=790, design_doc_name="test_doc_2", design_doc_type=DesignDocType.ORIGINAL
                ),
                DesignDocProcessorResult(
                    design_doc_id=791, design_doc_name="test_doc_3", design_doc_type=DesignDocType.ORIGINAL
                ),
            ]

            await job._notify_design_doc_flow_completion(True, design_doc_results)

            assert mock_send_notification.call_count == len(design_doc_results), (
                f"Expected {len(design_doc_results)} calls to _send_user_notification, "
                f"but got {mock_send_notification.call_count}"
            )

            expected_calls = [(("regular_user", account_id, doc.design_doc_id), {}) for doc in design_doc_results]
            assert mock_send_notification.call_args_list == expected_calls

    async def test_notify_design_doc_flow_completion_with_one_failed_notification(self, account_id: str):
        with patch.object(NotifyDesignReviewCompleted, "_send_mail_notification") as mock_notify:
            mock_notify.side_effect = [None, Exception("Failed to send notification"), None]

            with (
                patch.object(EventNotifier, "__init__", return_value=None),
                patch.object(EventNotifier, "notify", new_callable=AsyncMock),
                patch("service.jobs.base_job_logic.base_job_logic.get_redis_for_account", return_value=MagicMock()),
            ):
                job = DesignDocsJob(
                    account_id=account_id,
                    job_id=123,
                    design_doc_ids=None,
                    force=False,
                    service_dal=AsyncMock(),
                    source_id=456,
                )

                job._created_by = "regular_user"

                design_doc_results = [
                    DesignDocProcessorResult(
                        design_doc_id=789, design_doc_name="test_doc_1", design_doc_type=DesignDocType.ORIGINAL
                    ),
                    DesignDocProcessorResult(
                        design_doc_id=790, design_doc_name="test_doc_2", design_doc_type=DesignDocType.ORIGINAL
                    ),
                    DesignDocProcessorResult(
                        design_doc_id=791, design_doc_name="test_doc_3", design_doc_type=DesignDocType.ORIGINAL
                    ),
                ]

                await job._notify_design_doc_flow_completion(True, design_doc_results)

                assert mock_notify.call_count == len(design_doc_results), (
                    f"Expected {len(design_doc_results)} calls to notify, " f"but got {mock_notify.call_count}"
                )

    async def test_notify_design_doc_flow_completion_with_reference_doc(self, account_id: str):
        with (
            patch.object(DesignDocsJob, "_send_user_notification") as mock_send_notification,
            patch.object(EventNotifier, "__init__", return_value=None),
            patch.object(EventNotifier, "notify", new_callable=AsyncMock),
            patch("service.jobs.base_job_logic.base_job_logic.get_redis_for_account", return_value=MagicMock()),
        ):
            job = DesignDocsJob(
                account_id=account_id,
                job_id=123,
                design_doc_ids=None,
                force=False,
                service_dal=AsyncMock(),
                source_id=456,
            )

            job._created_by = "regular_user"

            design_doc_result = DesignDocProcessorResult(
                design_doc_id=789, design_doc_name="test_doc", design_doc_type=DesignDocType.REFERENCE
            )

            await job._notify_design_doc_flow_completion(True, [design_doc_result])

            mock_send_notification.assert_not_called()

    async def test_filter_exceptions_from_processors_results(self, account_id: str):
        processor1 = MagicMock()
        processor1.design_doc_id = 1
        processor1.origin_id = "doc1"
        processor1.design_doc_type = DesignDocType.ORIGINAL
        processor1.process = AsyncMock(return_value=None)
        processor1.number_of_steps = 1

        processor2 = MagicMock()
        processor2.design_doc_id = 2
        processor2.origin_id = "doc2"
        processor2.design_doc_type = DesignDocType.ORIGINAL
        processor2.process = AsyncMock(side_effect=Exception("Failed to process"))
        processor2.number_of_steps = 1

        processor3 = MagicMock()
        processor3.design_doc_id = 3
        processor3.origin_id = "doc3"
        processor3.design_doc_type = DesignDocType.ORIGINAL
        processor3.process = AsyncMock(return_value=None)
        processor3.number_of_steps = 1

        mock_gather = MagicMock()

        mock_redis = MagicMock()
        mock_redis.set = AsyncMock()

        with (
            patch("prime_utils.AsyncRateLimit", return_value=mock_gather),
            patch.object(
                DesignDocsJob, "_get_design_doc_to_process", new_callable=AsyncMock
            ) as mock_get_design_doc_to_process,
            patch.object(DesignDocsJob, "_prepare_processor", new_callable=AsyncMock) as mock_prepare_processor,
            patch.object(DesignDocsJob, "_setup_progress_step"),
            patch("service.jobs.base_job_logic.base_job_logic.get_redis_for_account", return_value=mock_redis),
        ):
            job = DesignDocsJob(
                account_id=account_id,
                job_id=123,
                design_doc_ids=None,
                force=False,
                service_dal=AsyncMock(),
                source_id=456,
            )

            job._progress_manager = MagicMock()
            job._progress_manager.increase_step_progress = AsyncMock()

            mock_get_design_doc_to_process.return_value = [1, 2, 3]

            mock_prepare_processor.side_effect = [processor1, processor2, processor3]

            mock_gather.gather_tasks.return_value = [
                processor1.process(),
                processor2.process(),
                processor3.process(),
            ]

            result = await job._process_design_docs()

            assert len(result) == 2
            assert result[0].design_doc_id == 1
            assert result[0].design_doc_name == "doc1"
            assert result[1].design_doc_id == 3
            assert result[1].design_doc_name == "doc3"

            mock_get_design_doc_to_process.assert_called_once()

            assert mock_prepare_processor.call_count == 3
