import contextlib
import logging
from collections.abc import As<PERSON><PERSON>enerator, Generator
from typing import Any
from unittest.mock import AsyncMock, <PERSON><PERSON>

import pytest
from prime_gen_ai_service_client import (
    CeleryHeaders,
    ContainerTaskOutput,
    DesignDocumentProcessTaskOutput,
    DocumentQuoteList,
    PoliciesRecommendation,
    PolicyRecommendationsFlowOutput,
    PolicyRecommendationsTaskOutput,
    RecommendationsPrioritizationFlowOutput,
    RecommendationsPrioritizationOutput,
    TopRecommendationWithReason,
)
from prime_gen_ai_service_client import (
    Issue as GenAIIssue,
)
from prime_redis_utils import AsyncPrefixRedisClient
from prime_tests import MockResponse, service_mocker
from prime_utils.progress_manager import ProgressManager

from service.db import ServiceDAL
from service.jobs.base_job_logic.gen_ai_celery import (
    GENAI_DESIGN_DOCUMENT_PROCESSING_TASK_NAME,
    GENAI_POLICY_RECOMMENDATIONS_TASK_NAME,
)
from service.jobs.base_job_logic.gen_ai_celery.celery_globals import GENAI_CONTAINER_PROCESSING_TASK_NAME
from service.jobs.design_docs_job.design_doc_processor import DesignDocProcessorContainer, DesignDocProcessorGeneral
from service.jobs.design_docs_job.utils import get_redis_processing_progress_percent_key
from service.models.design_docs import DesignDocType
from tests.mock_utils import (
    DESIGN_DOCS_SOURCE_ID,
    FILE_DATA,
    ORIGIN_ID,
    POLICY_DATA,
    get_external_cases,
    get_zip_data,
    mock_policy_service,
    mock_source_service,
    resources_dir,
)

from .desing_docs_helper import add_design_doc
from .job_test_utils import generic_mock_celery_manager, mock_version

LOGGER = logging.Logger("test")


def get_recommendations_priority_output() -> RecommendationsPrioritizationOutput:
    recommendations = [
        TopRecommendationWithReason(
            id=i,
            title="Test Title",
            description="Test Description",
            component="component",
            concern="concern",
            explanation="explanation",
            evidence="evidence",
            quotes=DocumentQuoteList(),
            references=[1],
        )
        for i in range(0, 5)
    ]
    return RecommendationsPrioritizationOutput(
        results=RecommendationsPrioritizationFlowOutput(recommendations=recommendations)
    )


def get_policy_recommendations_output() -> PolicyRecommendationsTaskOutput:
    recommendations = [
        PoliciesRecommendation(id=i, title="Test Title", description="Test Description", quotes=None)
        for i in range(101, 104)
    ]
    return PolicyRecommendationsTaskOutput(results=PolicyRecommendationsFlowOutput(recommendations=recommendations))


@contextlib.asynccontextmanager
async def mock_design_doc_processor_container(
    service_dal_fixture, account_id: str
) -> AsyncGenerator[tuple[DesignDocProcessorContainer, list[Any]]]:
    case_id = 1
    design_doc = await service_dal_fixture.design_docs_dal.add_design_doc(
        account_id,
        "title",
        "created_by",
        origin_id=ORIGIN_ID,
        case_id=case_id,
        doc_source_type=DesignDocType.CONTAINER,
    )

    with (
        service_mocker("file-manager-service") as file_manager_mocker,
        mock_version(),
        mock_celery_manager() as messages,
        mock_source_service(),
        mock_policy_service(),
        service_mocker("rat-logic-service") as rat_logic_mocker,
    ):
        file_manager_mocker.get(f"/files/{account_id}/data/source/-1/origin_id/{ORIGIN_ID}", MockResponse(FILE_DATA))
        policy_zip_data = get_zip_data({"policy1": POLICY_DATA})
        file_manager_mocker.put(f"files/{account_id}/data/source/-1", MockResponse(policy_zip_data))

        case = get_external_cases(account_id, [1])[0]
        rat_logic_mocker.get(f"/cases/{account_id}/case/1", MockResponse(case.model_dump()))
        progress_manager = Mock(spec=ProgressManager)
        progress_manager.progress = 100

        design_docs_processor = DesignDocProcessorContainer(
            account_id=account_id,
            design_doc_id=design_doc.id,
            origin_id=ORIGIN_ID,
            headers=AsyncMock(spec=CeleryHeaders),
            source_id=DESIGN_DOCS_SOURCE_ID,
            progress_manager=progress_manager,
            children=[GenAIIssue(id="ISSUE-1", summary="some summary", description="des", type="issue")],
            case=case,
            doc_source_type=design_doc.doc_source_type,
        )
        yield design_docs_processor, messages


@contextlib.asynccontextmanager
async def mock_design_doc_processor(
    service_dal_fixture, account_id: str
) -> AsyncGenerator[tuple[DesignDocProcessorGeneral, list[Any]]]:
    design_doc = await add_design_doc(service_dal_fixture, account_id, origin_id=ORIGIN_ID)
    with (
        service_mocker("file-manager-service") as file_manager_mocker,
        mock_version(),
        mock_celery_manager() as messages,
        mock_source_service(),
        mock_policy_service(),
    ):
        file_manager_mocker.get(f"/files/{account_id}/data/source/-2/origin_id/{ORIGIN_ID}", MockResponse(FILE_DATA))
        policy_zip_data = get_zip_data({"policy1": POLICY_DATA})
        file_manager_mocker.put(f"files/{account_id}/data/source/-1", MockResponse(policy_zip_data))

        progress_manager = Mock(spec=ProgressManager)
        progress_manager.progress = 100

        design_docs_processor = DesignDocProcessorGeneral(
            account_id=account_id,
            design_doc_id=design_doc.id,
            origin_id=ORIGIN_ID,
            headers=AsyncMock(spec=CeleryHeaders),
            source_id=DESIGN_DOCS_SOURCE_ID,
            progress_manager=progress_manager,
            doc_source_type=design_doc.doc_source_type,
        )
        yield design_docs_processor, messages


def get_security_review_container_output() -> ContainerTaskOutput:
    output_file = resources_dir / "ai_result_designdoc_container.json"
    output = output_file.read_text()
    return ContainerTaskOutput.model_validate_json(output)


def get_security_review_doc_output() -> DesignDocumentProcessTaskOutput:
    output_file = resources_dir / "ai_result_designdoc_general.json"
    output = output_file.read_text()
    return DesignDocumentProcessTaskOutput.model_validate_json(output)


@contextlib.contextmanager
def mock_celery_manager() -> Generator[list[Any]]:
    security_review_doc_output = get_security_review_doc_output()
    security_review_container_output = get_security_review_container_output()

    policy_recommendations_output = get_policy_recommendations_output()

    responses = {
        GENAI_DESIGN_DOCUMENT_PROCESSING_TASK_NAME: security_review_doc_output.model_dump(),
        GENAI_CONTAINER_PROCESSING_TASK_NAME: security_review_container_output.model_dump(),
        GENAI_POLICY_RECOMMENDATIONS_TASK_NAME: policy_recommendations_output.model_dump(),
    }
    with generic_mock_celery_manager(responses) as messages:
        yield messages


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestDesignDocsJobFlow:
    async def test_design_doc_container_full_flow(
        self, account_id: str, service_dal_fixture: ServiceDAL, account_redis_client: AsyncPrefixRedisClient
    ):
        async with mock_design_doc_processor_container(service_dal_fixture, account_id) as (design_docs_processor, _):
            await design_docs_processor.process()

        service_dal_fixture.session.expunge_all()
        doc = await service_dal_fixture.design_docs_dal.get_design_docs_by_id(
            account_id, design_docs_processor.design_doc_id
        )
        assert doc.mermaid_diagram

        # assert doc.summary == "I am partial questions summary"
        assert doc.title == "title"
        assert len(doc.top_recommendations) == 8
        assert len(doc.policy_recommendations) == 3
        assert len(doc.attack_scenarios) == 5
        assert doc.top_recommendations[0].attack_scenarios_names == [
            "attack_scenario_1",
            "attack_scenario_2",
            "attack_scenario_3",
            "attack_scenario_4",
            "attack_scenario_5",
        ]

        # assert await account_redis_client.get(get_redis_processing_progress_percent_key(doc.id))

    async def test_design_doc_full_flow(
        self, account_id: str, service_dal_fixture: ServiceDAL, account_redis_client: AsyncPrefixRedisClient
    ):
        async with mock_design_doc_processor(service_dal_fixture, account_id) as (design_docs_processor, messages):
            await design_docs_processor.process()

        service_dal_fixture.session.expunge_all()
        doc = await service_dal_fixture.design_docs_dal.get_design_docs_by_id(
            account_id, design_docs_processor.design_doc_id
        )
        assert doc.mermaid_diagram

        assert doc.title == "title"

        assert len(doc.top_recommendations) == 10
        assert len(doc.policy_recommendations) == 3
        assert len(doc.attack_scenarios) == 5
        assert doc.top_recommendations[0].attack_scenarios_names == ["attack_scenario_1"]

        for top_recommendation, policy_recommendation in zip(
            doc.top_recommendations, doc.policy_recommendations, strict=False
        ):
            assert top_recommendation.attack_scenarios_names == policy_recommendation.attack_scenarios_names
            assert top_recommendation.concern == policy_recommendation.concern
            assert top_recommendation.component == policy_recommendation.component
            assert top_recommendation.references == policy_recommendation.references
            assert top_recommendation.quotes == policy_recommendation.quotes

        assert await account_redis_client.get(get_redis_processing_progress_percent_key(doc.id))
