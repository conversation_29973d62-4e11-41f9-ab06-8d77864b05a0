import json

import pytest
from prime_gen_ai_service_client import ContentInner
from pydantic_ai.messages import (
    BinaryContent,
    ModelRequest,
    ModelResponse,
    SystemPromptPart,
    TextPart,
    UserPromptPart,
)
from pydantic_core import to_jsonable_python

from service.db.service_dal import ServiceDAL
from service.jobs.design_docs_job.agent_context import AgentContext

TEXT_MSG = "is it critical?"


@pytest.fixture
def agent_context_fixture(account_id: str, design_doc_id: int = 1) -> AgentContext:
    return AgentContext(account_id, design_doc_id)


def _get_msg_id(msg_id: int = 0) -> str:
    return f"{TEXT_MSG} {msg_id}"


def text_msg(msg_id: int = 0, parts: int = 1) -> ModelRequest:
    return ModelRequest(parts=[UserPromptPart(content=_get_msg_id(msg_id + i)) for i in range(parts)])


def response_msg(msg_id: int = 0) -> ModelResponse:
    return ModelResponse(parts=[TextPart(content=_get_msg_id(msg_id))])


def system_msg(msg_id: int = 0) -> ModelRequest:
    return ModelRequest(
        parts=[
            SystemPromptPart(
                content=_get_msg_id(msg_id),
            )
        ]
    )


def binary_msg(msg_id: int = 0) -> ModelRequest:
    return ModelRequest(
        parts=[
            UserPromptPart(
                content=[
                    _get_msg_id(msg_id),
                    BinaryContent(
                        data=b"test binary data",
                        media_type="application/pdf",
                    ),
                ]
            )
        ]
    )


class TestAgentContext:
    def test_ignore_model_response(self) -> None:
        assert AgentContext.get_message_content_for_flows(response_msg()) is None

    def test_get_message_content_for_flows_user_prompt_string(self) -> None:
        result = AgentContext.get_message_content_for_flows(text_msg())

        assert result is not None
        assert len(result.content) == 1
        assert result.content[0].actual_instance == _get_msg_id(0)

    def test_get_message_content_for_flows_user_prompt_list_string(self) -> None:
        """Test extracting content from UserPromptPart with list containing string."""
        result = AgentContext.get_message_content_for_flows(text_msg(1, 2))

        assert result is not None
        assert len(result.content) == 2
        assert result.content[0].actual_instance == _get_msg_id(1)
        assert result.content[1].actual_instance == _get_msg_id(2)

    def test_get_message_content_for_flows_binary_content(self) -> None:
        """Test extracting content from BinaryContent."""
        result = AgentContext.get_message_content_for_flows(binary_msg())

        assert result is not None
        assert len(result.content) == 2
        assert result.content[0].actual_instance == _get_msg_id(0)
        assert isinstance(result.content[1], ContentInner)

        doc = result.content[1].actual_instance
        assert doc.file_bytes == b"test binary data"
        assert doc.file_format == "pdf"
        assert doc.file_name == ""

    def test_get_message_content_for_flows_no_extractable_content(self) -> None:
        """Test that messages with no extractable content return None."""
        assert AgentContext.get_message_content_for_flows(system_msg()) is None

    def test_get_message_content_as_dict(self, agent_context_fixture: AgentContext) -> None:
        """Test that messages with no extractable content return None."""
        model_dict = to_jsonable_python(text_msg(), bytes_mode="base64")

        result = agent_context_fixture._extract_message_content(model_dict)

        assert len(result.content) == 1
        assert result.content[0].actual_instance == _get_msg_id(0)

    async def test_process_conversation_messages_single_text_message(self, agent_context_fixture: AgentContext) -> None:
        """Test processing conversation with single text message."""
        # Create a conversation with one text message
        user_msg_1 = text_msg(1)
        response_msg_1 = response_msg(1)
        user_msg_2 = binary_msg(2)
        response_msg_2 = response_msg(2)

        conversation_data = [user_msg_1, response_msg_1, user_msg_2, response_msg_2]

        conversation_data_json = [
            to_jsonable_python(text_message, bytes_mode="base64") for text_message in conversation_data
        ]
        conversation_data_str = json.dumps(conversation_data_json)

        result = await agent_context_fixture._process_conversation_messages(conversation_data_str)

        assert result is not None
        assert len(result) == 2
        user_msg_1_result = result[0]
        assert len(user_msg_1_result.content) == 1
        assert user_msg_1_result.content[0].actual_instance == _get_msg_id(1)

        user_msg_2_result = result[1]
        assert len(user_msg_2_result.content) == 2
        assert user_msg_2_result.content[0].actual_instance == _get_msg_id(2)
        binary_content_result = user_msg_2_result.content[1]
        assert binary_content_result.actual_instance is not None
        assert binary_content_result.actual_instance.file_bytes == b"test binary data"
        assert binary_content_result.actual_instance.file_format == "pdf"
        assert binary_content_result.actual_instance.file_name == ""

    async def test_process_conversation_messages_empty_conversation(self, agent_context_fixture: AgentContext) -> None:
        assert await agent_context_fixture._process_conversation_messages(json.dumps([])) is None

    async def test_process_conversation_no_doc_exists(
        self, agent_context_fixture: AgentContext, service_dal_fixture: ServiceDAL
    ) -> None:
        assert await agent_context_fixture.get_context() is None

    async def test_process_conversation_no_context_exists(
        self, account_id: str, agent_context_fixture: AgentContext, service_dal_fixture: ServiceDAL
    ) -> None:
        doc = await service_dal_fixture.design_docs_dal.add_design_doc(
            account_id, "title", "created_by", origin_id="origin_id"
        )
        agent_context_fixture._design_doc_id = doc.id
        assert await agent_context_fixture.get_context() is None

    async def test_process_conversation_no_conv_found(
        self, account_id: str, agent_context_fixture: AgentContext, service_dal_fixture: ServiceDAL
    ) -> None:
        doc = await service_dal_fixture.design_docs_dal.add_design_doc(
            account_id, "title", "created_by", origin_id="origin_id"
        )
        doc.agent_conversation_id = 5
        await service_dal_fixture.session.commit()
        agent_context_fixture._design_doc_id = doc.id
        assert await agent_context_fixture.get_context() is None
