import logging
import os
from unittest.mock import ANY, AsyncMock, patch

import pytest
from prime_tests import EnvSave
from pydantic import ValidationError

from service.jobs.design_docs_job import DesignDocsJob, DesignDocsJobArgs
from service.jobs.design_docs_job.main import main
from tests.mock_utils import DESIGN_DOCS_SOURCE_ID, mock_source_service

LOGGER = logging.Logger("test")


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestDesignDocsJob:
    async def test_main_logic(self, account_id: str):
        with (
            patch("service.jobs.design_docs_job.main.DesignDocsJob", autospec=True) as mock_design_docs,
            EnvSave(),
            mock_source_service(),
        ):
            job_instance_mock = AsyncMock()
            mock_design_docs.return_value = job_instance_mock
            args = DesignDocsJobArgs(account_id=account_id, job_id=1, force=False)
            os.environ.update({key: str(value) for key, value in args.model_dump(exclude_none=True).items()})
            await main()
            mock_design_docs.assert_called_once_with(
                account_id=account_id,
                job_id=1,
                design_doc_ids=None,
                force=False,
                service_dal=ANY,
                source_id=DESIGN_DOCS_SOURCE_ID,
            )
            job_instance_mock.start.assert_called_once()

    async def test_main_logic_no_args(self):
        with (
            patch.object(DesignDocsJob, "start", new_callable=AsyncMock),
            EnvSave(),
            pytest.raises(ValidationError),
        ):
            await main()
