import contextlib
import functools
import json
import random
import zipfile
from collections.abc import Generator
from datetime import UTC, datetime
from io import Bytes<PERSON>
from pathlib import Path
from urllib.parse import parse_qs, urlparse

from dateutil import parser as datetime_parser
from orjson import orjson
from prime_chatbot_service_client import ConversationOutput
from prime_file_manager_service_client import ChildLink, DocumentType, FileInfo
from prime_rat_logic_service_client import (
    CaseStatus,
    ConfidenceScoreLevel,
    ExternalCase,
    ExternalCaseWorkroom,
    ExternalIssueAnalysis,
    ExternalIssueAnalysisWorkroom,
    PaginationResponseExternalCaseWorkroom,
    RiskFactors,
    RiskScoreCategory,
    Summary5W,
)
from prime_service_kit.fastapi_utils.pagination import PaginationResponse
from prime_source_service_client import JiraConnectionDetails
from prime_source_service_client.models import SourceModel, SourceType
from prime_tests import MockResponse, ServiceMocked, service_mocker

from service.services_clients import FILE_INFO_BATCH_SIZE

resources_dir = Path(__file__).parent / "_resources"


class RefString:
    def __init__(self, value: str):
        self.value = value

    def get(self) -> str:
        return self.value

    def set(self, value: str) -> None:
        self.value = value


ACCOUNT_ID_CONTEXT = RefString("")


def reset_account_id():
    ACCOUNT_ID_CONTEXT.set(f"test_account_id_{random.randint(1, 100000)}")


SOURCE_ID = 10
GOOGLE_SOURCE_ID = 11
DESIGN_DOCS_SOURCE_ID = -2
JIRA_HOST = "https://prime-test.atlassian.net"
JOB_ID = 1

ORIGIN_ID = "origin_id"
FILE_DATA = b"design_doc_data"
POLICY_DATA = b"policy_data"


def get_issue_id(i: int) -> str:
    return f"ISSUE-{i}"


POLICY_RECOMMENDATION = json.loads((resources_dir / "policy_recommendations.json").read_text())


def mock_get_descendants_empty(file_manager_mocker: ServiceMocked, account_id: str):
    file_manager_mocker.get(f"/relationship/{account_id}/source/*/origin_id/*/descendants", MockResponse([]))


def mock_get_descendants_links(file_manager_mocker: ServiceMocked, account_id: str) -> list[ChildLink]:
    response = [
        ChildLink(link="http://stam_link1", link_type=None, id=1),
        ChildLink(link="http://stam_link2", link_type=DocumentType.JIRA, id=2),
        ChildLink(link="http://stam_link3", link_type=DocumentType.GDRIVE, id=2),
        ChildLink(link="http://stam_link4", link_type=DocumentType.CONFLUENCE, id=None),
    ]
    file_manager_mocker.get(
        f"/relationship/{account_id}/source/*/origin_id/*/descendants-links",
        MockResponse(json.dumps([x.model_dump() for x in response])),
    )
    return response


def get_zip_data(files: dict[str, bytes]) -> bytes:
    zip_buffer = BytesIO()
    zip_file = zipfile.ZipFile(zip_buffer, "a")
    for file_name, issue_data in files.items():
        zip_file.writestr(file_name, issue_data)
    zip_file.close()
    zip_buffer.seek(0)
    return zip_buffer.read()


def _set_files_mock(
    all_files: list[tuple[str, bytes]],
    file_manager_mocker: ServiceMocked,
    account_id: str,
    timestamp: datetime | dict[str, datetime] = datetime.now(UTC),
    document_type: DocumentType = DocumentType.JIRA,
) -> None:
    file_infos = []
    for file_id, (file_name, issue_data) in enumerate(all_files):
        origin_id = f"{file_name}.json"
        download_file_url = f"files/{account_id}/data/{file_id}"
        file_manager_mocker.get(download_file_url, MockResponse(issue_data))
        download_origin_url = f"files/{account_id}/data/source/{SOURCE_ID}/origin_id/{origin_id}"
        file_manager_mocker.get(download_origin_url, MockResponse(issue_data))
        file_timestamp = timestamp.get(origin_id, datetime.now(UTC)) if isinstance(timestamp, dict) else timestamp
        file_infos.append(
            FileInfo(
                id=file_id,
                origin_id=origin_id,
                domain="primesec.ai",
                timestamp=file_timestamp,
                downloadable_link=f"https://primesec.ai/{origin_id}",
                document_type=document_type,
            )
        )

    def _data_response(*args, **kwargs) -> MockResponse:
        file_names = args[3].get("file_names")
        _files = {}
        for _file_name, _issue_data in all_files:
            origin_id = f"{_file_name}.json"
            if file_names is None or origin_id in file_names:
                _files[origin_id] = _issue_data
        return MockResponse(get_zip_data(_files))

    file_manager_mocker.put(f"files/{account_id}/data/source/*", _data_response)
    file_manager_mocker.put(f"files/{account_id}/data", _data_response)

    def _info_response(*args, **kwargs) -> MockResponse:
        _file_infos = kwargs.get("file_infos")
        if since := parse_qs(urlparse(args[1]).query).get("since"):
            since = datetime_parser.parse(since[0])
        since_file_infos = [info for info in _file_infos if since is None or info.timestamp > since]
        info_response = PaginationResponse(
            results=[x.model_dump(by_alias=True) for x in since_file_infos],
            size=len(since_file_infos),
            limit=FILE_INFO_BATCH_SIZE,
            start=0,
            total=len(since_file_infos),
            has_next=False,
        )
        return MockResponse(info_response.model_dump_json())

    file_manager_mocker.put(f"files/{account_id}/info*", functools.partial(_info_response, file_infos=file_infos))


@contextlib.contextmanager
def _basic_mocks(
    *,
    account_id: str | None = None,
) -> Generator[tuple[ServiceMocked, ServiceMocked, ServiceMocked]]:
    account_id = account_id or ACCOUNT_ID_CONTEXT.get()
    with (
        service_mocker("file-manager-service") as file_manager_mocker,
        service_mocker("config-service") as config_mocker,
        mock_source_service(account_id) as source_mocker,
    ):
        yield file_manager_mocker, source_mocker, config_mocker


@contextlib.contextmanager
def mock_policy_service(account_id: str | None = None):
    account_id = account_id or ACCOUNT_ID_CONTEXT.get()
    policy_zip_data = get_zip_data({"policy1": POLICY_DATA})
    with service_mocker("policy-service") as policy_mocker:
        policy_mocker.get(f"/{account_id}/policies/policy1/data", MockResponse(POLICY_DATA))
        policy_mocker.get(f"/{account_id}/policies/download_all", MockResponse(policy_zip_data))
        yield


def get_external_cases(account_id: str, cases_ids: list[int]) -> list[ExternalCase]:
    cases = []
    for case in get_external_cases_workroom(account_id, cases_ids):
        summary_5w = Summary5W.model_validate(
            {
                "what": {"summary": "This is a summary", "description": "This is a detailed description"},
                "where": {"environment": "Environment", "components": "Components affected", "products": "Products"},
                "who": {"stakeholders": "Person stakeholders", "affected": "Person affected"},
                "why": {"purpose": "Purpose of the issue.", "impact": "Impact of the issue."},
                "how": {"approach": "Approach taken.", "acceptance": "Acceptance criteria."},
            }
        )
        analysis = ExternalIssueAnalysis(
            **case.issue_analysis.model_dump(),
            long_ai_summary_5w=summary_5w,
            short_ai_summary="Short summary",
            short_assessment="",
            long_assessment="",
        )
        case_dump = case.model_dump(exclude={"issue_analysis"})
        cases.append(
            ExternalCase(
                **case_dump, issue_analysis=analysis, framework_concerns={}, prime_concerns=[], history=[], comments=[]
            )
        )
    return cases


def get_external_cases_workroom(account_id: str, cases_ids: list[int]) -> list[ExternalCaseWorkroom]:
    cases = []
    risk_factors = RiskFactors(
        confidentiality_level=None,
        integrity_level=None,
        availability_level=None,
        third_party_management_level=None,
        compliance_level=None,
    )
    for case_id in cases_ids:
        analysis = ExternalIssueAnalysisWorkroom(
            account_id=account_id,
            source_id=SOURCE_ID,
            issue_id=f"ISSUE-{case_id}",
            risk_factors=risk_factors,
            risk_score_category=RiskScoreCategory.ANALYZE,
            confidence_level=ConfidenceScoreLevel.LOW,
            is_automated=False,
        )
        case = ExternalCaseWorkroom(
            case_id=case_id,
            account_id=account_id,
            source_id=10,
            issue_id=f"ISSUE-{case_id}",
            status=CaseStatus.OPEN,
            title="title",
            write_back_recommendations=False,
            link=f"https://utrl/ISSUE-{case_id}",
            labels=[],
            provider_fields={},
            issue_analysis=analysis,
            progress_percentage=0,
        )
        cases.append(case)
    return cases


@contextlib.contextmanager
def mock_cases_data(account_id: str, case_ids: list[int]) -> Generator[ServiceMocked]:
    account_id = account_id or ACCOUNT_ID_CONTEXT.get()
    cases = get_external_cases_workroom(account_id, case_ids)
    with service_mocker("rat-logic-service") as rat_logic_mocker:
        resp = PaginationResponseExternalCaseWorkroom(results=cases, size=len(cases), limit=100, start=0)
        rat_logic_mocker.get(f"/cases/{account_id}?*", MockResponse(resp.model_dump()))
        yield rat_logic_mocker


@contextlib.contextmanager
def mock_source_service(account_id: str | None = None) -> Generator[ServiceMocked]:
    account_id = account_id or ACCOUNT_ID_CONTEXT.get()
    now = datetime.now(UTC)
    with service_mocker("source-service") as source_mocker:
        # JIRA
        jira_source = SourceModel(id=SOURCE_ID, source_type=SourceType.JIRA, created_at=now)
        source_mocker.get(f"/sources/{account_id}/{SOURCE_ID}", MockResponse(jira_source.model_dump()))
        connection = JiraConnectionDetails(jira_url=JIRA_HOST, email="<EMAIL>", api_token="123456")
        conn_mocked_resp = MockResponse(connection.model_dump())
        source_mocker.get(f"/sources/{account_id}/{SOURCE_ID}/connection_details/", conn_mocked_resp)

        # POLICY
        policy_source = SourceModel(id=-1, source_type=SourceType.POLICY, created_at=now)
        source_mocker.get(f"/sources/{account_id}/-1", MockResponse(policy_source.model_dump()))

        # DESIGN_DOCS
        design_docs_source = SourceModel(id=DESIGN_DOCS_SOURCE_ID, source_type=SourceType.DESIGNDOCS, created_at=now)
        source_mocker.get(
            f"/sources/{account_id}/{DESIGN_DOCS_SOURCE_ID}", MockResponse(design_docs_source.model_dump())
        )

        # GOOGLE
        google_source = SourceModel(id=GOOGLE_SOURCE_ID, source_type=SourceType.GOOGLE, created_at=now)
        source_mocker.get(f"/sources/{account_id}/{GOOGLE_SOURCE_ID}", MockResponse(google_source.model_dump()))

        def _sources_response(*args, **kwargs) -> MockResponse:
            _source_type = parse_qs(urlparse(args[1]).query).get("source_type")
            resp = []
            if SourceType.JIRA in _source_type:
                resp.append(jira_source.model_dump())
            if SourceType.POLICY in _source_type:
                resp.append(policy_source.model_dump())
            if SourceType.DESIGNDOCS in _source_type:
                resp.append(design_docs_source.model_dump())
            if SourceType.GOOGLE in _source_type:
                resp.append(google_source.model_dump())
            return MockResponse(orjson.dumps(resp))

        source_mocker.get(f"/sources/{account_id}/{SOURCE_ID}/domain", MockResponse("https://test.com"))
        source_mocker.get(f"/sources/{account_id}?*", _sources_response)
        sources_list_mocked_resp = MockResponse(
            orjson.dumps([jira_source.model_dump(), design_docs_source.model_dump()])
        )
        source_mocker.get(f"/sources/{account_id}", sources_list_mocked_resp)
        yield source_mocker


@contextlib.contextmanager
def _agent_api_mocks(
    account_id: str | None = None,
    conv_id: int | None = None,
) -> Generator[ServiceMocked]:
    account_id = account_id or ACCOUNT_ID_CONTEXT.get()
    with service_mocker("chatbot-service") as chatbot_mocker:
        if conv_id:
            now = datetime.now(UTC)
            chatbot_mocker.get(
                f"/agent/{account_id}/conversation/{conv_id}",
                MockResponse(
                    ConversationOutput(conversation_id=conv_id, created_at=now, updated_at=now).model_dump_json()
                ),
            )

        yield chatbot_mocker
