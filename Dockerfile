# Build stage
FROM 797808101759.dkr.ecr.eu-central-1.amazonaws.com/base-linux-image:latest AS builder

RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev  \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

RUN mkdir -p /app/src/service && touch /app/src/service/__init__.py

COPY poetry.lock pyproject.toml ./

# Install Poetry
ENV POETRY_HOME=/opt/poetry
RUN curl -sSL https://install.python-poetry.org | python3 -
ENV PATH="${POETRY_HOME}/bin:${PATH}"

RUN poetry config virtualenvs.create false && \
    poetry config installer.max-workers 10

RUN --mount=type=secret,id=CODEARTIFACT_TOKEN,required=true \
    export POETRY_HTTP_BASIC_MAIN_REPO_PASSWORD=$(cat /run/secrets/CODEARTIFACT_TOKEN) && \
    export POETRY_HTTP_BASIC_MAIN_REPO_USERNAME=aws && \
    poetry install --only=main --no-cache --no-interaction &&  \
    rm -rf ~/.cache/pypoetry/cache &&  \
    rm -rf ~/.cache/pypoetry/artifacts

COPY ./src /app/src

# Runtime stage
FROM 797808101759.dkr.ecr.eu-central-1.amazonaws.com/base-linux-image:latest

WORKDIR /app

# Copy the installed dependencies and source code from the builder stage
COPY --from=builder /usr/local /usr/local
COPY --from=builder /app /app

RUN useradd -m appuser
USER appuser

CMD ["fastapi", "run", "/app/src/service/service_app.py", "--proxy-headers", "--port", "8000"]