[tool.poetry]
name = "security-review-service"
version = "0.1.2"
description = ""
authors = ["<PERSON><PERSON> <<EMAIL>>"]
packages = [{ from = "src", include = "service" }]
requires-poetry = ">=2.1"

[tool.poetry.dependencies]
python = "^3.13"
pydantic = "^2.11"
python-multipart = "^0"
sqlmodel = "*"
prime-service-kit = { version = "*", source = "main_repo" }
prime-db-utils = { version = "*", source = "main_repo" }
prime-shared = { version = "*", source = "main_repo" }
prime-utils = { version = "*", source = "main_repo" }
prime-jobs = { version = "*", source = "main_repo" }
datadog = "^0.51.0"
msgpack = "^1.1.0"
ddtrace = "3.9.1"
prime-notification-service-client = { version = "*", source = "main_repo" }
prime-config-service-client = { version = "*", source = "main_repo" }
prime-fetcher-service-client = { version = "*", source = "main_repo" }
prime-celery = { version = "*", source = "main_repo" }
prime-source-service-client = { version = "*", source = "main_repo" }
prime-events = { version = "*", source = "main_repo" }
prime-gen-ai-service-client = { version = "*", source = "main_repo" }
prime-file-manager-service-client = { version = "*", source = "main_repo" }
prime-rat-logic-service-client = { version = "*", source = "main_repo" }
prime-policy-service-client = { version = "*", source = "main_repo" }
prime-chatbot-service-client ={ version = "*", source = "main_repo" }

pypdf = "^5.6.0"
pydantic-ai-slim = { extras = ["bedrock"], version = "^0.2.16" }


[tool.poetry.group.dev.dependencies]
prime-security-review-service-client = { path = "client", develop = true }
aiohttp = "^3.12.12"
frozenlist = "^1.7.0"
prime-tests = { version = "*", source = "main_repo" }

[tool.poetry.scripts]
server = "src.service.main:main"
generate_service_openapi = "src.service.generate_openapi:main"



[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[[tool.poetry.source]]
name = "main_repo"
url = "https://primesec-797808101759.d.codeartifact.eu-central-1.amazonaws.com/pypi/main-repo/simple/"
priority = "supplemental"

[[tool.poetry.source]]
name = "main_repo_publish"
url = "https://primesec-797808101759.d.codeartifact.eu-central-1.amazonaws.com/pypi/main-repo/"
priority = "supplemental"