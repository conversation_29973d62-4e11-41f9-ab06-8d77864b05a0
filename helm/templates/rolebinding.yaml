
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ .Values.global.service.name }}-role-binding
  annotations:
      "helm.sh/hook": pre-install,pre-upgrade
      "helm.sh/hook-weight": "-80"
subjects:
- kind: ServiceAccount
  name: {{ .Values.global.service.name }}-sa
roleRef:
  kind: Role
  name: {{ .Values.global.service.name }}-role
  apiGroup: rbac.authorization.k8s.io