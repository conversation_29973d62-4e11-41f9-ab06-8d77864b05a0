apiVersion: bitnami.com/v1alpha1
kind: SealedSecret
metadata:
  annotations:
    sealedsecrets.bitnami.com/cluster-wide: "true"
    sealedsecrets.bitnami.com/skip-set-owner-references: "true"
    sealedsecrets.bitnami.com/managed: "true"
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "-99"
  creationTimestamp: null
  name: "{{ .Values.global.service.name }}-secrets"
spec:
  encryptedData:
    {{- if .Values.global.celery_redis }}
    CELERY_REDIS__REDIS_PASSWORD: "{{ .Values.global.celery_redis.password }}"
    {{- end }}
  template:
    metadata:
      annotations:
        sealedsecrets.bitnami.com/cluster-wide: "true"
        sealedsecrets.bitnami.com/skip-set-owner-references: "true"
        sealedsecrets.bitnami.com/managed: "true"
        "helm.sh/hook": pre-install,pre-upgrade
        "helm.sh/hook-weight": "-99"
      creationTimestamp: null
      name: "{{ .Values.global.service.name }}-secrets"
