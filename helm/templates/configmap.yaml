apiVersion: v1
kind: ConfigMap
metadata:
  name: "{{ .Values.global.service.name }}-env-vars"
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "-99"
data:
  IMAGE_TAG: "{{ .Values.global.image.registry }}/{{ .Values.global.image.name }}:{{ .Values.global.image.tag }}"
  JOBS_IMAGE_URL: "{{ .Values.global.image.registry }}/{{ .Values.global.image.name }}:{{ .Values.global.image.tag }}"
  MAX_WORKERS: "{{ .Values.global.tasks.max_workers }}"
  EXPORT_LIMIT: "{{ .Values.global.tasks.export_limit }}"
  BUILD_FIELDS_DATA_SAMPLE_SIZE: "{{ .Values.global.tasks.build_fields_data_sample_size }}"
{{- if .Values.global.celery_redis }}
  CELERY_REDIS__REDIS_HOSTNAME: "{{ .Values.global.celery_redis.hostname }}"
{{- end }}
{{- if .Values.global.langfuse.langfuse_enabled }}
  LANGFUSE_PUBLIC_KEY: "{{ .Values.global.langfuse.public_key }}"
  LANGFUSE_HOST: "{{ .Values.global.langfuse.host }}"
{{- end }}
