import logging
from logging.config import fileConfig

import alembic_postgresql_enum  # noqa: F401
from alembic import context
from prime_db_utils import DBSettings, build_db_url
from prime_shared.common_dataclasses import EnvironmentTypes
from service.config import get_config
from service.db import SQLModel
from sqlalchemy import create_engine, pool

config = context.config

if config.config_file_name is not None and get_config().service_environment != EnvironmentTypes.test:
    fileConfig(config.config_file_name)

target_metadata = SQLModel.metadata

LOGGER = logging.getLogger()


def get_url() -> str:
    return build_db_url(DBSettings(), False)


def run_migrations_offline() -> None:
    LOGGER.info("run_migrations_offline")
    url = get_url()
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    LOGGER.info("run_migrations_online")
    connectable = create_engine(get_url(), poolclass=pool.NullPool)

    with connectable.connect() as connection:
        context.configure(connection=connection, target_metadata=target_metadata)
        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
