"""autogenerated

Revision ID: 250604_2159
Revises: 
Create Date: 2025-06-04 21:59:15.973125

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel

from service.models.design_docs import (AttackScenario,
                                        DesignDocTopPolicyRecommendation,
                                        DesignDocTopRecommendation)
from service.db.tables.utils.json_as_pydantic import JSONAsPydantic

from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '250604_2159'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('CRON_JOB_DUP', 'USER_CANCELED', 'FLOW_ALREADY_RUNNING', name='jobcancelreason').create(op.get_bind())
    sa.Enum('SCHEDULED', 'PENDING', 'RUNNING', 'FINALIZING', 'COMPLETED', 'FAILED', 'CANCELED', name='jobstatus').create(op.get_bind())
    op.create_table('design_docs',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('account_id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('file_origin_id', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('title', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('summary', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('created_by', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('mermaid_diagram', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('url', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('attack_scenario_dataflow_diagram', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('attack_scenario_markdown', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('top_recommendations', postgresql.ARRAY(JSONAsPydantic(DesignDocTopRecommendation)), nullable=True),
    sa.Column('policy_recommendations', postgresql.ARRAY(JSONAsPydantic(DesignDocTopPolicyRecommendation)), nullable=True),
    sa.Column('attack_scenario_recommendations', postgresql.ARRAY(JSONAsPydantic(AttackScenario)), nullable=True),
    sa.Column('attack_scenarios', postgresql.ARRAY(JSONAsPydantic(AttackScenario)), nullable=True),
    sa.Column('case_id', sa.Integer(), nullable=True),
    sa.Column('agent_conversation_id', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('account_id', 'file_origin_id', name='account_file_origin_id_unique_constraint')
    )
    op.create_index(op.f('ix_design_docs_account_id'), 'design_docs', ['account_id'], unique=False)
    op.create_index(op.f('ix_design_docs_case_id'), 'design_docs', ['case_id'], unique=False)
    op.create_index(op.f('ix_design_docs_created_at'), 'design_docs', ['created_at'], unique=False)
    op.create_index(op.f('ix_design_docs_deleted_at'), 'design_docs', ['deleted_at'], unique=False)
    op.create_index(op.f('ix_design_docs_updated_at'), 'design_docs', ['updated_at'], unique=False)
    op.create_table('jobs',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('account_id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('job_name', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('status', postgresql.ENUM('SCHEDULED', 'PENDING', 'RUNNING', 'FINALIZING', 'COMPLETED', 'FAILED', 'CANCELED', name='jobstatus', create_type=False), nullable=False),
    sa.Column('error', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('job_class', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('retry', sa.Integer(), nullable=False),
    sa.Column('job_group_id', sa.Uuid(), nullable=False),
    sa.Column('cron_job_group_id', sa.Integer(), nullable=True),
    sa.Column('job_args', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('cancel_reason', postgresql.ENUM('CRON_JOB_DUP', 'USER_CANCELED', 'FLOW_ALREADY_RUNNING', name='jobcancelreason', create_type=False), nullable=True),
    sa.Column('run_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('created_by', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    sa.Column('job_type', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('job_config', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_jobs_account_id'), 'jobs', ['account_id'], unique=False)
    op.create_index(op.f('ix_jobs_created_at'), 'jobs', ['created_at'], unique=False)
    op.create_index(op.f('ix_jobs_cron_job_group_id'), 'jobs', ['cron_job_group_id'], unique=False)
    op.create_index(op.f('ix_jobs_deleted_at'), 'jobs', ['deleted_at'], unique=False)
    op.create_index(op.f('ix_jobs_updated_at'), 'jobs', ['updated_at'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_jobs_updated_at'), table_name='jobs')
    op.drop_index(op.f('ix_jobs_deleted_at'), table_name='jobs')
    op.drop_index(op.f('ix_jobs_cron_job_group_id'), table_name='jobs')
    op.drop_index(op.f('ix_jobs_created_at'), table_name='jobs')
    op.drop_index(op.f('ix_jobs_account_id'), table_name='jobs')
    op.drop_table('jobs')
    op.drop_index(op.f('ix_design_docs_updated_at'), table_name='design_docs')
    op.drop_index(op.f('ix_design_docs_deleted_at'), table_name='design_docs')
    op.drop_index(op.f('ix_design_docs_created_at'), table_name='design_docs')
    op.drop_index(op.f('ix_design_docs_case_id'), table_name='design_docs')
    op.drop_index(op.f('ix_design_docs_account_id'), table_name='design_docs')
    op.drop_table('design_docs')
    sa.Enum('SCHEDULED', 'PENDING', 'RUNNING', 'FINALIZING', 'COMPLETED', 'FAILED', 'CANCELED', name='jobstatus').drop(op.get_bind())
    sa.Enum('CRON_JOB_DUP', 'USER_CANCELED', 'FLOW_ALREADY_RUNNING', name='jobcancelreason').drop(op.get_bind())
    # ### end Alembic commands ###
