"""autogenerated

Revision ID: 250605_1631
Revises: 250604_2159
Create Date: 2025-06-05 16:31:44.935001

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel

from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '250605_1631'
down_revision: Union[str, None] = '250604_2159'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('ORIGINAL', 'REFERENCE', 'CONTAINER', 'URL', name='designdoctype').create(op.get_bind())
    op.add_column('design_docs', sa.Column('doc_source_type', postgresql.ENUM('ORIGINAL', 'REFERENCE', 'CONTAINER', 'URL', name='designdoctype', create_type=False), nullable=False))
    op.create_index(op.f('ix_design_docs_doc_source_type'), 'design_docs', ['doc_source_type'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_design_docs_doc_source_type'), table_name='design_docs')
    op.drop_column('design_docs', 'doc_source_type')
    sa.Enum('ORIGINAL', 'REFERENCE', 'CONTAINER', 'URL', name='designdoctype').drop(op.get_bind())
    # ### end Alembic commands ###
