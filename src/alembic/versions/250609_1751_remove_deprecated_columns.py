"""autogenerated

Revision ID: 250609_1751
Revises: 250608_1228
Create Date: 2025-06-09 17:51:35.817578

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel

from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '250609_1751'
down_revision: Union[str, None] = '250608_1228'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('design_docs', 'attack_scenario_markdown')
    op.drop_column('design_docs', 'attack_scenario_recommendations')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('design_docs', sa.Column('attack_scenario_recommendations', postgresql.ARRAY(postgresql.JSON(astext_type=sa.Text())), autoincrement=False, nullable=True))
    op.add_column('design_docs', sa.Column('attack_scenario_markdown', sa.VARCHAR(), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
