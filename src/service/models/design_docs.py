from __future__ import annotations

from datetime import datetime
from enum import StrEnum, auto

from prime_jobs import JobStatus
from prime_shared.common_types import SourceIdType
from pydantic import BaseModel


class DesignDocProcessStatus(BaseModel):
    processing_progress_percent: float
    job_status: JobStatus
    timestamp: datetime


class DesignDocType(StrEnum):
    ORIGINAL = auto()
    REFERENCE = auto()
    CONTAINER = auto()
    URL = auto()


class ContextType(StrEnum):
    AGENT = auto()


class AttackScenario(BaseModel):
    id: int
    name: str
    markdown: str
    dataflow_diagram: str
    recommendations: list[DesignDocTopRecommendation]


class DesignDocRecommendationBase(BaseModel):
    id: int
    title: str
    description: str
    evidence: str


class DesignDocTopRecommendation(DesignDocRecommendationBase):
    references: list[int] | None = None
    component: str | None = None
    concern: str | None = None
    quotes: list[str] | None = None
    attack_scenarios_names: list[str] | None = None


class DesignDocPolicyRecommendationQuote(BaseModel):
    quote_text: str
    quote_source: str


class DesignDocTopPolicyRecommendation(DesignDocTopRecommendation):
    policy_quotes: list[DesignDocPolicyRecommendationQuote]


class DesignDocByMultipleUrlsReq(BaseModel):
    urls: list[str]
    process_as_one: bool = False
    created_by: str


class DesignDocResponse(BaseModel):
    id: int
    title: str
    case_id: int | None = None
    created_by: str
    created_at: datetime
    updated_at: datetime
    file_origin_id: str | None
    url: str | None = None
    issue_id: str | None = None
    source_id: int | None = None
    doc_source_type: DesignDocType
    jira_issue_url: str | None = None
    processing_status: DesignDocProcessStatus | None = None


class DesignDocPreviewDAL(BaseModel):
    id: int
    title: str
    created_by: str
    created_at: datetime
    updated_at: datetime
    file_origin_id: str | None
    url: str | None = None
    case_id: int | None = None
    doc_source_type: DesignDocType


class ExtendDesignDocResponse(DesignDocResponse):
    summary: str
    top_recommendations: list[DesignDocTopRecommendation] = []
    policy_recommendations: list[DesignDocTopPolicyRecommendation] = []
    mermaid_diagram: str | None = None
    attack_scenarios: list[AttackScenario] | None = None
    dataflow_diagram: str | None = None


class DesignDocFileUploadResponse(BaseModel):
    files_uploaded: int
    file_names: list[str]


class AttachContextToDesignDocReq(BaseModel):
    created_by: str
    context_type: ContextType
    context_id: int


class DesignDocBySingleUrlReq(BaseModel):
    created_by: str
    url: str


class CaseData(BaseModel):
    id: int
    source_id: SourceIdType | None
    issue_id: str | None
    jira_issue_url: str | None
