from __future__ import annotations

from prime_db_utils import AccountIdBaseTable, AuditHistoryBaseTable
from sqlalchemy import Column, UniqueConstraint
from sqlalchemy.dialects import postgresql
from sqlmodel import Field

from service.db.tables.utils.json_as_pydantic import JSONAsPydantic
from service.models.design_docs import (
    AttackScenario,
    DesignDocTopPolicyRecommendation,
    DesignDocTopRecommendation,
    DesignDocType,
)


class DesignDocsTable(AccountIdBaseTable, AuditHistoryBaseTable, table=True):
    __tablename__ = "design_docs"
    id: int = Field(primary_key=True)
    file_origin_id: str | None = Field(nullable=True)  # in case it's a file base design doc
    title: str = Field(nullable=False)
    summary: str = Field(nullable=True)
    description: str | None = Field(nullable=True)
    created_by: str = Field(nullable=False)
    mermaid_diagram: str | None = Field(nullable=True)
    url: str | None = Field(nullable=True)  # in case it's a url base design doc
    attack_scenario_dataflow_diagram: str | None = Field(nullable=True)

    top_recommendations: list[DesignDocTopRecommendation] | None = Field(
        default=None, sa_column=Column(postgresql.ARRAY(JSONAsPydantic(DesignDocTopRecommendation)))
    )
    policy_recommendations: list[DesignDocTopPolicyRecommendation] | None = Field(
        default=None, sa_column=Column(postgresql.ARRAY(JSONAsPydantic(DesignDocTopPolicyRecommendation)))
    )

    attack_scenarios: list[AttackScenario] | None = Field(
        default=None, sa_column=Column(postgresql.ARRAY(JSONAsPydantic(AttackScenario)))
    )
    case_id: int | None = Field(nullable=True, index=True)  # in case it's a container base design doc

    agent_conversation_id: int | None = Field(nullable=True)

    doc_source_type: DesignDocType = Field(index=True, default=DesignDocType.ORIGINAL)

    __table_args__ = (
        UniqueConstraint("account_id", "file_origin_id", name="account_file_origin_id_unique_constraint"),
    )
