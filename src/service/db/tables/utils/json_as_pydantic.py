from typing import Any, Optional

from pydantic import BaseModel
from sqlalchemy import <PERSON>alect, TypeDecorator
from sqlmodel import JSON


class JSONAsPydantic(TypeDecorator[Optional[BaseModel]]):
    impl = JSON
    cache_ok = False

    def __init__(self, pydantic_model: type[BaseModel], *args: Any, **kwargs: Any):
        super().__init__(*args, **kwargs)
        self.pydantic_model = pydantic_model

    def process_bind_param(self, value: Optional[Optional[BaseModel]], dialect: Dialect) -> Any:
        return value.model_dump(mode="json") if value is not None else None

    def process_result_value(self, value: Any, dialect: Dialect) -> Optional[BaseModel]:
        return self.pydantic_model.model_validate(value) if value is not None else None
