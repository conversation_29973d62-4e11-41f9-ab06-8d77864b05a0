from __future__ import annotations

import contextlib
import logging
from collections.abc import AsyncGenerator
from typing import TYPE_CHECKING, Annotated

from fastapi import Depends
from prime_db_utils import get_async_session
from prime_jobs import SchedulerDAL

from service.config import get_config

from .dal_design_docs import DesignDocsDAL

if TYPE_CHECKING:
    from sqlmodel.ext.asyncio.session import AsyncSession


LOGGER = logging.getLogger("service_dal")


class ServiceDAL:
    def __init__(self, session: AsyncSession):
        self._session = session
        self.scheduler_dal: SchedulerDAL = SchedulerDAL(self.session)
        self.design_docs_dal = DesignDocsDAL(self)

    @property
    def session(self) -> AsyncSession:
        return self._session


async def get_service_dal() -> AsyncGenerator[ServiceDAL]:
    LOGGER.info("Creating service DAL")
    async with get_async_session(get_config()) as async_session:
        yield ServiceDAL(async_session)
        await async_session.commit()


@contextlib.asynccontextmanager
async def get_service_dal_context() -> AsyncGenerator[ServiceDAL]:
    LOGGER.info("Creating service DAL")
    async with get_async_session(get_config()) as async_session:
        yield ServiceDAL(async_session)
        await async_session.commit()


service_dal_depends = Annotated[ServiceDAL, Depends(get_service_dal, use_cache=False)]
