from __future__ import annotations

import abc
import logging
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from sqlmodel.ext.asyncio.session import AsyncSession

    from service.db import ServiceDAL


LOGGER = logging.getLogger("rat-logic-service")


class DalBase(metaclass=abc.ABCMeta):  # noqa: B024
    def __init__(self, service_dal: ServiceDAL) -> None:
        self._service_dal = service_dal

    @property
    def _session(self) -> AsyncSession:
        return self._service_dal.session
