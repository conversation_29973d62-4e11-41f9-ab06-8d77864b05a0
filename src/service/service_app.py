from collections.abc import AsyncGenerator
from contextlib import asynccontextmanager

from fastapi import FastAPI
from prime_db_utils import init_tables
from prime_service_kit import get_app

from service.config import get_config
from service.routers import (
    design_docs_api,
)
from service.routers.jobs import jobs_api
from service.services_clients import close_clients

service_apis = [design_docs_api, jobs_api]


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None]:
    init_tables(get_config())
    yield
    await close_clients()


app = get_app(
    config=get_config(),
    authenticated_routers=[],
    public_routers=service_apis,
    lifespan=lifespan,
)
