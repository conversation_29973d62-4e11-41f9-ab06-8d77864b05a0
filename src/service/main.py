import uvicorn

# reubinoff: removing all classes at commit ef997416e238e24fbb3ac0b50a54881b9ffc269a
## / 9333c8a728aa2cddd0e245d327c553496fd2ec09
# will restore when policy will be relevant
from service.config import get_config


def main() -> None:
    uvicorn.run(
        "service.service_app:app",
        host="0.0.0.0",
        port=get_config().service_port,
        reload=get_config().service_reload,
    )


if __name__ == "__main__":
    main()
