import logging
from typing import Annotated

from fastapi import APIRouter, Query
from prime_jobs import JobStatus
from prime_jobs.shceduler_dep import scheduler_dal_dep, scheduler_dep
from prime_redis_utils import AsyncPrefixRedisClient
from prime_service_kit.fastapi_utils import audit_pagination_args_type
from prime_service_kit.shared_types import AccountArgumentType, SourceIdQuery
from prime_utils import AsyncRateLimit
from prime_utils.progress_manager import ProgressManager
from starlette import status

from service.db import Job
from service.dependencies import redis_type
from service.job_type import JobType
from service.logic import add_job_logic
from service.models import (
    JobDesignDocsCreateArgs,
)
from service.models.jobs import JobCreatedResponse, JobStatusResponse

jobs_api = APIRouter(prefix="/jobs", tags=["jobs"])
LOGGER = logging.getLogger("jobs")
JobStatusesQ = Annotated[list[JobStatus], Query(alias="statuses")]


async def _get_job_progress(redis_client: AsyncPrefixRedisClient, job: Job) -> float:
    if job.status in [JobStatus.COMPLETED, JobStatus.FAILED]:
        return 100
    if job.status == JobStatus.PENDING:
        return 0
    if not job.id:
        return 0
    progress_manager = ProgressManager(redis_client, identifier=job.progress_manager_identifier)
    metadata = await progress_manager.read_from_cache()
    return metadata.progress


async def _get_job(job: Job, redis_client: AsyncPrefixRedisClient) -> JobStatusResponse:
    progress = await _get_job_progress(redis_client, job)
    return JobStatusResponse(
        name=JobType(job.job_type),
        id=job.id,  # type: ignore[arg-type]
        status=job.status,
        source_id=job.job_args.get("source_id", 0),
        created_at=job.created_at,  # type: ignore[arg-type]
        progress=int(progress),
        error=None,
        created_by=job.created_by,
    )


@jobs_api.get(
    "/{account_id}/{job_id}",
    status_code=status.HTTP_200_OK,
    description="Get job",
    name="get_job",
)
async def get_job(
    account_id: AccountArgumentType, job_id: int, scheduler_dal: scheduler_dal_dep, redis_client: redis_type
) -> JobStatusResponse:
    job = await scheduler_dal.jobs_dal.get_job_by_id(account_id, job_id)
    return await _get_job(job, redis_client)


@jobs_api.get(
    "/{account_id}",
    status_code=status.HTTP_200_OK,
    description="Get jobs",
    name="get_jobs",
)
async def get_jobs(
    account_id: AccountArgumentType,
    scheduler_dal: scheduler_dal_dep,
    redis_client: redis_type,
    pagination_args: audit_pagination_args_type,
    job_status: Annotated[list[JobStatus] | None, Query()] = None,
    source_id: SourceIdQuery | None = None,
) -> list[JobStatusResponse]:
    jobs_args: dict[str, str] | None = None
    if source_id:
        jobs_args = {"source_id": str(source_id)}
    jobs = await scheduler_dal.jobs_dal.get_jobs(
        account_id, job_status=job_status, pagination_args=pagination_args, job_args=jobs_args
    )
    jobs_response: list[JobStatusResponse] = list(
        await AsyncRateLimit(10).gather_tasks([_get_job(job, redis_client) for job in jobs])
    )
    return jobs_response


@jobs_api.post(
    "/{account_id}/",
    status_code=status.HTTP_201_CREATED,
    name="add_job",
    description="Adds a job for the given account and arguments",
    response_model=JobCreatedResponse,
)
async def add_job(
    account_id: AccountArgumentType,
    job_create_args: JobDesignDocsCreateArgs,
    scheduler: scheduler_dep,
) -> JobCreatedResponse:
    new_job = await add_job_logic(account_id, job_create_args, scheduler)
    return JobCreatedResponse(
        job_id=new_job.id,  # type: ignore[arg-type]
        status=new_job.status,
    )
