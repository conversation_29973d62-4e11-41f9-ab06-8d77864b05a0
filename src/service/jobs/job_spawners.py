import logging
from abc import ABC, abstractmethod
from datetime import datetime
from pathlib import Path
from typing import Any

from prime_jobs import (
    <PERSON><PERSON><PERSON>ob,
    JobCancelReason,
    JobConfig,
    JobInfo,
    JobNameType,
    JobSpawnerFactory,
    JobSpawnerHelmDeployer,
    ServiceInfo,
    scheduler_dal_ctx,
)
from prime_shared.common_types import AccountIdType
from pydantic import BaseModel, Field

from service.config import get_config
from service.job_type import JobType

from .design_docs_job.models import DesignDocsJobArgs

JOB_NAME_MAX_LENGTH = 53

LOGGER = logging.getLogger(__name__)

job_info_types = DesignDocsJobArgs


class SecurityReviewJobEnv(BaseModel):
    account_id: str
    source_id: int | None = None
    additional_env: dict[str, str] = Field(default_factory=dict)


class SecurityReviewJobInfo(JobInfo):
    module_path: str


class SecurityReviewJobSpawnerBase(JobSpawnerHelmDeployer, ABC):
    NAME: JobType

    def __init__(self, account_id: AccountIdType, *args: Any, **kwargs: Any) -> None:
        super().__init__(account_id, *args, **kwargs)
        self._account_id = account_id

    def get_helm_name(self, job_id: int) -> str:
        return self.get_job_name(job_id)

    def get_job_name(self, job_id: int) -> str:
        base_name = f"{self.NAME.value}-job-{job_id}-{self._account_id}"
        sanitized_name = base_name.replace("_", "-").lower()
        return sanitized_name[:JOB_NAME_MAX_LENGTH]

    @classmethod
    def job_path(cls) -> Path:
        return Path(__file__).parent / "helm"

    @property
    def helm_path(self) -> Path:
        return self.job_path()

    def _convert_to_helm_job_env(self, job_args: job_info_types) -> SecurityReviewJobEnv:
        additional_env = {}
        for key, value in job_args.model_dump().items():
            if key not in ["account_id", "job_id"] and value is not None:
                additional_env[key.upper()] = str(value)
        return SecurityReviewJobEnv(account_id=self._account_id, additional_env=additional_env)

    @abstractmethod
    async def _get_job_args(self, job_id: int) -> BaseModel:
        pass

    async def get_job_config(self, job_id: int) -> dict[str, Any]:
        return JobConfig(
            job_env=await self._get_job_args(job_id),
            job_info=SecurityReviewJobInfo(
                name=self.get_job_name(job_id),
                id=job_id,
                image=get_config().jobs_image_url,
                job_type=f"{self.NAME.value}-job",
                module_path=f"service.jobs.{self.NAME.value.replace('-', '_')}_job.main",
            ),
            service=ServiceInfo(name=get_config().service_name),
            datadog_enabled=get_config().datadog_enabled,
        ).model_dump(mode="json", serialize_as_any=True)

    async def _get_latest_completed_job_time(self, job_args: dict[str, str]) -> datetime | None:
        async with scheduler_dal_ctx() as scheduler_dal:
            latest_job = await scheduler_dal.jobs_dal.get_latest_completed_job(
                account_id=self._account_id,
                job_args=job_args,
                job_type=self.NAME.value,
            )
            return latest_job.created_at if latest_job else None


class SecurityReviewSpawnerBaseCancelDup(SecurityReviewJobSpawnerBase, ABC):
    @abstractmethod
    def get_unique_args(self) -> dict[str, Any] | None:
        pass

    async def spawn_job(self, job_id: int) -> JobNameType | None:
        unique_args = self.get_unique_args()
        async with scheduler_dal_ctx() as scheduler_dal:
            if await scheduler_dal.jobs_dal.any_active_jobs_by(
                self._account_id,
                job_args=unique_args,
                exclude_ids=[job_id],
                job_type=self.NAME.value,
            ):
                LOGGER.warning(
                    "There is already an active job with args: %s, canceling current job %s..",
                    unique_args,
                    job_id,
                )
                await scheduler_dal.jobs_dal.cancel_job(self._account_id, job_id, JobCancelReason.FLOW_ALREADY_RUNNING)
                return f"job-{job_id}-canceled"
        return await super().spawn_job(job_id)


class DesignDocsJobSpawner(SecurityReviewSpawnerBaseCancelDup):
    NAME = JobType.DESIGN_DOCS

    def __init__(self, account_id: AccountIdType, force: bool, design_doc_ids: list[int] | None = None) -> None:
        super().__init__(account_id, design_doc_ids=design_doc_ids, force=force)
        self._design_doc_ids = design_doc_ids
        self._force = force

    async def _get_job_args(self, job_id: int) -> SecurityReviewJobEnv:
        args = DesignDocsJobArgs(
            account_id=self._account_id, job_id=job_id, design_doc_ids=self._design_doc_ids, force=self._force
        )
        return self._convert_to_helm_job_env(args)

    def get_unique_args(self) -> dict[str, Any] | None:
        if self._design_doc_ids:
            unique_docs = sorted(set(self._design_doc_ids))
            return {"design_doc_ids": str(unique_docs)}
        return None


class SecurityReviewJobSpawnerFactory(JobSpawnerFactory):
    JOB_SPAWNERS: dict[JobType, type[SecurityReviewJobSpawnerBase]] = {
        JobType.DESIGN_DOCS: DesignDocsJobSpawner,
    }

    @classmethod
    async def get_job_spawner_class(cls, create_job: CreateJob) -> type[JobSpawnerHelmDeployer]:
        spawner_name = JobType(create_job.job_type)
        if spawner_class := cls.JOB_SPAWNERS.get(spawner_name):
            return spawner_class
        raise ValueError(f"Spawner not found for job {spawner_name}")
