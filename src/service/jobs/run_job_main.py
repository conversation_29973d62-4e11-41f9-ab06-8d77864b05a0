from __future__ import annotations

import asyncio
import logging
import os
import sys
from collections.abc import Callable, Coroutine
from typing import Any

from prime_logger import init_root_logger
from prime_redis_utils import get_redis
from prime_utils.monitoring import report_exception, set_application_monitoring

from service.config import get_config
from service.services_clients import close_clients

LOGGER = logging.getLogger("k8s-job")


async def close_all_clients(coroutine: Callable[[], Coroutine[Any, Any, None]]) -> None:
    try:
        await coroutine()
    finally:
        await get_redis(get_config()).aclose(close_connection_pool=True)
        await close_clients()


def run_main(coroutine: Callable[[], Coroutine[Any, Any, None]]) -> None:
    if os.environ.get("DATADOG_ENABLED"):
        set_application_monitoring(datadog_enabled=True)
    init_root_logger(get_config())
    try:
        asyncio.run(close_all_clients(coroutine))
    except Exception as e:  # noqa: E722
        LOGGER.exception("Failed to run main")
        report_exception(e)
        sys.exit(1)
    sys.exit(0)
