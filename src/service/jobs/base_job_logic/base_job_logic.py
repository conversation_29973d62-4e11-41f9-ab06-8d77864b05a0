from __future__ import annotations

import asyncio
import logging
import signal
import threading
from abc import ABC, abstractmethod
from collections.abc import Callable
from datetime import UTC, datetime
from functools import cached_property, lru_cache
from types import FrameType
from typing import Any, TypeVar, final

from prime_file_manager_service_client import DocumentType
from prime_jobs import JobStatus, UpdateJob
from prime_jobs.errors import JobNotFoundError
from prime_logger import PrimeAttributes, add_prime_attributes_context
from prime_redis_utils import get_redis_for_account
from prime_shared.common_types import AccountIdType, SourceIdType
from prime_source_service_client import SourceType
from prime_utils import alru_cache
from prime_utils.progress_manager import ProgressManager
from pydantic import BaseModel, Field

from service.config import get_config
from service.db import ServiceDAL
from service.errors import JobNotStartedError
from service.services_clients import ServicesClients

T = TypeVar("T", bound=Callable[..., Any])

LOGGER = logging.getLogger("k8s_job")


class JobPrimeAttributes(PrimeAttributes):
    job_type: str | None = Field(serialization_alias="x-prime-job-type", default=None)


class JobMetadata(BaseModel):
    job_id: int
    job_type: str
    account_id: AccountIdType
    source_id: SourceIdType | None = None


class BaseJobLogic(ABC):
    def __init__(
        self,
        account_id: AccountIdType,
        source_id: SourceIdType,
        job_id: int,
        service_dal: ServiceDAL,
    ) -> None:
        self._service_dal = service_dal
        self._account_id = account_id
        self._source_id = source_id
        self._job_id: int = job_id
        self._created_by: str = "prime"
        self._status = JobStatus.PENDING
        self._redis_client = get_redis_for_account(account_id, get_config(), get_config().namespace)
        self._progress_manager = ProgressManager(redis_cache=self._redis_client)
        self._job_metadata = JobMetadata(
            job_type=self.job_type, account_id=self._account_id, job_id=self._job_id, source_id=self._source_id
        )
        self._errors: dict[str, str] = {}
        self._init = False
        self._start_time = datetime.now(UTC)
        self._set_shutdown_event()
        LOGGER.info("Initialized job (%s) account_id %s, source_id %s", self.job_type, account_id, source_id)

    @alru_cache
    async def source_type(self) -> SourceType:
        return (await ServicesClients.source_api().get_source(self._account_id, self._source_id)).source_type

    @alru_cache
    async def document_type(self) -> DocumentType:
        source_type = await self.source_type()
        if source_type == SourceType.JIRA:
            return DocumentType.JIRA
        return DocumentType.ISSUE  # design docs flow

    @property
    def created_by(self) -> str:
        return self._created_by

    @property
    def duration(self) -> int:
        return (datetime.now(UTC) - self._start_time).seconds

    def _set_shutdown_event(self) -> None:
        if threading.current_thread() is threading.main_thread():
            for sig in (signal.SIGTERM, signal.SIGINT):
                signal.signal(sig, self._signal_received)
        self._shutdown_event = asyncio.Event()

    def _signal_received(self, sig: int, frame: FrameType | None) -> None:
        LOGGER.warning("Received termination signal %s", sig)
        if asyncio.get_running_loop():
            asyncio.create_task(self.termination_signal_handler())
        else:
            asyncio.run(self.termination_signal_handler())

    @abstractmethod
    async def termination_signal_handler(self) -> None:
        pass

    @cached_property
    def job_id(self) -> int:
        return self.get_job_id()

    @property
    def progress(self) -> float:
        return self._progress_manager.progress

    @property
    @abstractmethod
    def job_type(self) -> str:
        pass

    @property
    def status(self) -> JobStatus:
        return self._status

    @lru_cache
    def get_job_id(self) -> int:
        if self._init:
            return self._job_id
        raise JobNotStartedError

    @abstractmethod
    async def update_redis_status_on_finish(self, job_status: JobStatus) -> None:
        pass

    @abstractmethod
    async def update_redis_status_on_start(self) -> None:
        pass

    @final
    async def start(self) -> None:
        attributes = JobPrimeAttributes(
            account_id=self._account_id, source_id=self._source_id, job_id=self._job_id, job_type=self.job_type
        )
        with add_prime_attributes_context(attributes):
            await self._init_job()
            LOGGER.info("Starting job (%s)", self.job_type)
            try:
                await self._update_status(JobStatus.RUNNING)
                await self._run()
                await self.report(True)
            except Exception as e:
                LOGGER.exception("Job failed (%s)", self.job_type)
                status = JobStatus.FAILED
                await self.update_redis_status_on_finish(status)
                await self._update_status(status, str(e))
                await self.report(False)
                raise
            else:
                LOGGER.info("Finalizing job (%s)", self.job_type)
                status = JobStatus.COMPLETED
                await self.update_redis_status_on_finish(status)
                await self._update_status(status)
            finally:
                await self._on_task_done()

    @abstractmethod
    async def _run(self) -> None:
        raise NotImplementedError

    async def _update_status(self, status: JobStatus, error_message: str | None = None) -> None:
        if status == JobStatus.COMPLETED and self._errors:
            error_message = f"{error_message or ''} {str(self._errors)}".strip()
        await self._service_dal.scheduler_dal.jobs_dal.update_job(
            self._account_id, self.get_job_id(), UpdateJob(status=status)
        )
        self._status = status
        if error_message:
            await self._service_dal.scheduler_dal.jobs_dal.update_job(
                self._account_id, self.get_job_id(), UpdateJob(error=error_message[:1000])
            )

    def _setup_progress_step(self, progress_step: float) -> None:
        self._progress_manager.update_progress_step_value(progress_step)

    async def _increase_step_progress(self) -> None:
        await self._progress_manager.increase_step_progress()

    async def _init_job(self) -> int:
        if self._init:
            return self._job_id
        try:
            job = await self._service_dal.scheduler_dal.jobs_dal.get_job_by_id(self._account_id, self._job_id)
        except JobNotFoundError:
            raise JobNotStartedError from None
        self._progress_manager.update_identifier(job.progress_manager_identifier)
        self._init = True
        self._created_by = job.created_by
        return self._job_id

    async def _on_task_done(self) -> None:
        LOGGER.info("Job completed (%s) with status %s, finalizing...", self.job_type, self.status)
        await self._progress_manager.complete()

    @abstractmethod
    async def report(self, success: bool) -> None:
        pass
