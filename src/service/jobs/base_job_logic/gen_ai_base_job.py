import logging
from abc import ABC, abstractmethod
from functools import lru_cache

from packaging.version import Version, parse
from prime_celery import PrimeCelery
from prime_gen_ai_service_client import CeleryHeaders

from service.config import get_config

from .base_job_logic import BaseJobLogic
from .gen_ai_celery import GENAI_PIPELINE_VERSION_TASK_NAME, GENAI_VERSION_QUEUE

LOGGER = logging.getLogger("gen_ai_base_job")


class GenAIBaseJob(BaseJobLogic, ABC):
    def _get_celery_headers(self, issue_id: str, results_queue_name: str, additional: dict[str, str]) -> CeleryHeaders:
        return CeleryHeaders(
            issue_id=issue_id,
            account_id=self._account_id,
            source_id=self._source_id,
            job_id=self._job_id,
            user_id=self._created_by,
            results_queue_name=results_queue_name,
            additional_properties=additional,
        )

    @lru_cache
    @staticmethod
    def get_ai_version() -> Version:
        LOGGER.info("Getting ai version")
        app_name = f"{get_config().namespace}-base-get-version"
        celery_app = PrimeCelery(app_name, get_config(), get_config().celery_redis)
        celery_app._app.conf.task_routes = {GENAI_PIPELINE_VERSION_TASK_NAME: {"queue": GENAI_VERSION_QUEUE}}
        gen_ai_version_task = celery_app.send_task(GENAI_PIPELINE_VERSION_TASK_NAME, queue=GENAI_VERSION_QUEUE)
        ai_version = Version(**(gen_ai_version_task.get(timeout=60)))
        LOGGER.info("AI version is %s", ai_version)
        return ai_version

    @lru_cache
    @staticmethod
    def version_changed(current_version: str | None, ai_version: Version | None) -> bool:
        if not current_version or not ai_version:
            return True
        current_version_parsed = parse(current_version)
        return current_version_parsed.major < ai_version.major or current_version_parsed.minor < ai_version.minor

    @abstractmethod
    async def report(self, execution_status: bool) -> None:
        pass

    async def termination_signal_handler(self) -> None:
        try:
            await self.report(False)
        except Exception:
            LOGGER.exception("Error during termination handling")
        finally:
            self._shutdown_event.set()
