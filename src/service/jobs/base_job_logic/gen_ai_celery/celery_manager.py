from __future__ import annotations

import asyncio
import logging
import time
from typing import Any

from celery.exceptions import TimeoutError as CeleryTimeoutError
from celery.result import AsyncResult
from prime_celery import PrimeCelery
from prime_gen_ai_service_client import (
    CeleryHeaders,
)

from service.config import get_config
from service.errors import AIGenerationError, AIGenerationTimeoutError

from .celery_globals import (
    GENAI_DESIGN_DOCUMENT_PROCESSING_TASK_NAME,
    GENAI_PARTIALLY_SUMMARY_TASK_NAME,
    GENAI_PIPELINE_RUN_TASK_NAME,
    GENAI_PIPELINES_QUEUE,
)

TASK_TIMEOUT_SEC = 60 * 60 * 6  # 6 hours
TASK_SLEEP_PERIOD_SEC = 2
GET_RESULT_TIMEOUT_SEC = 10  # 10 seconds

LOGGER = logging.getLogger("gen_ai_manager")


class CeleryManager:
    """Handle all Celery-related operations"""

    def __init__(self, celery_app_name: str):
        self._celery_app_name = celery_app_name
        self._celery_app: PrimeCelery = self._load_celery_app(celery_app_name)

    def _load_celery_app(self, celery_app_name: str) -> PrimeCelery:
        celery_app = PrimeCelery(celery_app_name, get_config(), get_config().celery_redis)
        celery_app._app.conf.task_routes = {
            GENAI_PARTIALLY_SUMMARY_TASK_NAME: {"queue": GENAI_PIPELINES_QUEUE},
            GENAI_DESIGN_DOCUMENT_PROCESSING_TASK_NAME: {"queue": GENAI_PIPELINES_QUEUE},
            GENAI_PIPELINE_RUN_TASK_NAME: {"queue": GENAI_PIPELINES_QUEUE},
        }
        return celery_app

    @staticmethod
    async def _wait_for_result(issue_id: str, result: AsyncResult) -> dict[str, Any]:
        task_id = result.task_id
        LOGGER.debug("Waiting for AI result for issue id %s, task id %s", issue_id, task_id)
        start_time = time.time()
        try:
            time_counter = 0.0
            while not result.ready():
                await asyncio.sleep(TASK_SLEEP_PERIOD_SEC)
                time_counter += TASK_SLEEP_PERIOD_SEC
                if time_counter > TASK_TIMEOUT_SEC:
                    raise AIGenerationTimeoutError(issue_id)

            ai_result = result.get(timeout=GET_RESULT_TIMEOUT_SEC)
            result.forget()
            return dict(ai_result)
        except CeleryTimeoutError as e:
            LOGGER.exception("Error raised while waiting for AI result for issue id %s", issue_id)
            raise AIGenerationTimeoutError(issue_id) from e
        finally:
            running_sec = time.time() - start_time
            LOGGER.debug("AI result for issue id %s, task id %s, took %s seconds", issue_id, task_id, running_sec)

    def _send_task(
        self,
        input_args: dict[str, Any],
        headers: CeleryHeaders,
        task_name: str,
        queue: str,
        priority: int | None = None,
        task_id: str | None = None,
    ) -> AsyncResult:
        result = self._celery_app.send_task(
            task_name,
            kwargs={"task_input_dict": input_args},
            headers=headers.to_dict(),
            queue=queue,
            priority=priority,
            task_id=task_id,
        )
        return result

    def get_active_tasks(self) -> dict[str, Any]:
        return self._celery_app.get_active_tasks()

    async def send_and_wait_for_result(
        self,
        input_args: dict[str, Any],
        headers: CeleryHeaders,
        task_name: str,
        queue: str,
        priority: int | None = None,
        task_id: str | None = None,
    ) -> dict[str, Any]:
        LOGGER.debug("Sending task [%s] to queue [%s]", task_name, queue)
        result = self._send_task(input_args, headers, task_name, queue, task_id=task_id, priority=priority)
        try:
            return await self._wait_for_result(headers.issue_id, result)
        except AIGenerationTimeoutError:
            LOGGER.warning("AI generation timeout for issue id %s", headers.issue_id)
            raise
        except Exception as e:
            LOGGER.exception("Error raised while waiting for AI result for issue id %s", headers.issue_id)
            raise AIGenerationError(
                account_id=headers.account_id,
                source_id=headers.source_id,
                issue_id=headers.issue_id,
            ) from e
