from prime_shared.common_types import AccountIdType, SourceIdType

from service.config import get_config

GENAI_PIPELINE_PREFIX = "genai-service"
GENAI_PIPELINES_AI_PREFIX = f"{GENAI_PIPELINE_PREFIX}.ai"


# Queues
GENAI_PIPELINES_QUEUE = f"{get_config().namespace}_genai_pipelines"
GENAI_VERSION_QUEUE = f"{get_config().namespace}_genai_short_tasks"

# Task names
GENAI_PIPELINE_VERSION_TASK_NAME = f"{GENAI_PIPELINE_PREFIX}.version"

GENAI_PIPELINE_RUN_TASK_NAME = f"{GENAI_PIPELINES_AI_PREFIX}.run"
GENAI_PARTIALLY_SUMMARY_TASK_NAME = f"{GENAI_PIPELINES_AI_PREFIX}.context"
GENAI_DESIGN_DOCUMENT_PROCESSING_TASK_NAME = f"{GENAI_PIPELINES_AI_PREFIX}.design_document_processing"
GENAI_CONTAINER_PROCESSING_TASK_NAME = f"{GENAI_PIPELINES_AI_PREFIX}.container_processing"
GENAI_POLICY_RECOMMENDATIONS_TASK_NAME = f"{GENAI_PIPELINES_AI_PREFIX}.policy_recommendations"

RESULTS_TASK_NAME = "security-review.results"

# Celery app name
CELERY_APP_NAME_PREFIX = f"security-review-{get_config().namespace}"


def get_results_queue_name(account_id: AccountIdType, source_id: SourceIdType) -> str:
    return f"ai_results_{get_config().namespace}_{account_id}_{source_id}"
