import logging
from datetime import UTC, datetime
from typing import cast

from prime_gen_ai_service_client import (
    AttackScenario,
    AttackScenarioRecommendation,
    DesignDocument,
    PoliciesRecommendation,
    PolicyDocument,
    TopRecommendation,
)
from prime_jobs import JobStatus
from prime_redis_utils import AsyncPrefixRedisClient
from prime_shared.common_types import AccountIdType, SourceIdType
from prime_source_service_client import SourceType
from prime_utils import unzip_data

from service.config import get_config
from service.models import (
    DesignDocPolicyRecommendationQuote,
    DesignDocTopPolicyRecommendation,
    DesignDocTopRecommendation,
)
from service.models.design_docs import DesignDocProcessStatus
from service.services_clients import ServicesClients

LOGGER = logging.getLogger("design_doc")
SECONDS_IN_DAY = 60 * 60 * 24  # 60 seconds * 60 minutes * 24 hours


def build_attack_scenarios_names_for_top_recommendations(
    top_recommendations: list[DesignDocTopRecommendation],
    all_rec_attack_scenario: list[AttackScenarioRecommendation] | list[AttackScenarioRecommendation],
    attack_scenarios: list[AttackScenario],
) -> None:
    LOGGER.debug(
        "Build attack scenarios for recommendations - top_recommendations - [%s],"
        " all_rec_attack_scenario - [%s], attack_scenarios - [%s]",
        top_recommendations,
        all_rec_attack_scenario,
        attack_scenarios,
    )
    attack_scenario_id_to_name = {}
    for attack_scenario in attack_scenarios:
        attack_scenario_id_to_name[attack_scenario.unique_id] = attack_scenario.name

    all_recommendations_id_to_attack_scenario_name = {}
    for rec in all_rec_attack_scenario:
        all_recommendations_id_to_attack_scenario_name[rec.id] = attack_scenario_id_to_name[rec.attack_scenario_id]

    for top_recommendation in top_recommendations:
        if not top_recommendation.attack_scenarios_names:
            top_recommendation.attack_scenarios_names = []

        for reference in top_recommendation.references:  # type: ignore[union-attr]
            attack_scenario_name = all_recommendations_id_to_attack_scenario_name[reference]
            if attack_scenario_name not in top_recommendation.attack_scenarios_names:
                top_recommendation.attack_scenarios_names.append(attack_scenario_name)


def to_design_doc_recommendations(
    recommendations: list[TopRecommendation],
) -> list[DesignDocTopRecommendation]:
    LOGGER.info("Top recommendations output: %s", recommendations)

    recommendation_to_save = []
    for rec in recommendations:
        quotes = rec.quote_list.quote or []
        recommendation_to_save.append(
            DesignDocTopRecommendation(
                id=cast(int, rec.id),
                title=rec.title,
                description=rec.description,
                references=rec.references,
                component=rec.component,
                concern=rec.concern,
                evidence=rec.evidence,
                quotes=quotes,
                attack_scenarios_names=[],
            )
        )
    return recommendation_to_save


def to_policy_design_recommendations(
    ai_policy_recommendations: list[PoliciesRecommendation], recommendations: list[DesignDocTopRecommendation]
) -> list[DesignDocTopPolicyRecommendation]:
    if not ai_policy_recommendations:
        LOGGER.info("No policy recommendations found")
        return []
    recommendations_dict = {rec.id: rec for rec in recommendations}
    policy_recommendations: list[DesignDocTopPolicyRecommendation] = []
    for policy_rec in ai_policy_recommendations:
        if policy_rec.quotes and policy_rec.quotes.quote:
            policy_quotes = [
                DesignDocPolicyRecommendationQuote(quote_text=quote.text, quote_source=quote.source)
                for quote in policy_rec.quotes.quote
                if quote.text and quote.source
            ]
        else:
            policy_quotes = []
        top_recommendation = recommendations_dict[policy_rec.id]
        policy_recommendations.append(
            DesignDocTopPolicyRecommendation(
                id=policy_rec.id,
                title=policy_rec.title,
                description=policy_rec.description,
                evidence=top_recommendation.evidence,
                policy_quotes=policy_quotes,
                attack_scenarios_names=top_recommendation.attack_scenarios_names,
                concern=top_recommendation.concern,
                component=top_recommendation.component,
                references=top_recommendation.references,
                quotes=top_recommendation.quotes,
            )
        )
    return policy_recommendations


async def get_design_doc(account_id: AccountIdType, source_id: SourceIdType, origin_id: str) -> DesignDocument:
    file_bytes = await ServicesClients.files_api().download_origin_file_for_source(
        account_id, source_id, origin_id=origin_id
    )
    return DesignDocument(file_name=origin_id, file_format="pdf", file_bytes=file_bytes)


async def get_policies(account_id: AccountIdType) -> list[PolicyDocument]:
    LOGGER.info("Getting policies for account %s", account_id)
    policy_source = await ServicesClients.source_api().get_sources(account_id, SourceType.POLICY)
    if not policy_source:
        LOGGER.warning("No policy source found for account %s", account_id)
        return []
    policy_files = await ServicesClients.files_api().download_files_for_source(account_id, policy_source[0].id)
    unzipped_files = unzip_data(policy_files)
    return [
        PolicyDocument(file_name=policy_name, file_format="pdf", file_bytes=policy_content)
        for policy_name, policy_content in unzipped_files.items()
    ]


async def update_redis_process_status(
    job_status: JobStatus, current_progress: float, redis_client: AsyncPrefixRedisClient, design_doc_id: int
) -> None:
    design_doc_redis_status = DesignDocProcessStatus(
        processing_progress_percent=current_progress, job_status=job_status, timestamp=datetime.now(UTC)
    )
    await redis_client.set(
        get_redis_processing_progress_percent_key(design_doc_id),
        design_doc_redis_status.model_dump_json(),
        ex=SECONDS_IN_DAY,
    )


def get_redis_processing_progress_percent_key(design_doc_id: int) -> str:
    config = get_config()
    return f"design_doc_processing_progress:{config.namespace}:{design_doc_id}"
