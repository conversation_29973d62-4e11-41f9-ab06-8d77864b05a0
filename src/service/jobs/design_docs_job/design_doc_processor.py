from __future__ import annotations

import logging
from abc import ABC, abstractmethod
from collections.abc import Sequence

from prime_gen_ai_service_client import (
    AttackScenario as GenAIAttackScenario,
)
from prime_gen_ai_service_client import (
    AttackScenarioRecommendation,
    CeleryHeaders,
    ContainerTaskInput,
    ContainerTaskOutput,
    DesignDocumentProcessTaskInput,
    DesignDocumentProcessTaskOutput,
    PolicyRecommendationsTaskInput,
    PolicyRecommendationsTaskOutput,
    Recommendation,
)
from prime_gen_ai_service_client import (
    Issue as GenAIssue,  # noqa: E501
)
from prime_jobs import JobStatus
from prime_rat_logic_service_client import ExternalCase
from prime_rat_logic_service_client import Issue as RatLogicIssue
from prime_redis_utils import get_redis_for_account
from prime_shared.common_types import AccountIdType, SourceIdType
from prime_utils.progress_manager import ProgressManager

from service.config import get_config
from service.db import get_service_dal_context
from service.jobs.design_docs_job.agent_context import <PERSON><PERSON>ontex<PERSON>
from service.models.design_docs import AttackScenario, DesignDocTopRecommendation, DesignDocType

from ..base_job_logic.gen_ai_celery import (
    GENAI_DESIGN_DOCUMENT_PROCESSING_TASK_NAME,
    celery_manager_instance,
)
from ..base_job_logic.gen_ai_celery.celery_globals import (
    GENAI_CONTAINER_PROCESSING_TASK_NAME,
    GENAI_PIPELINES_QUEUE,
    GENAI_POLICY_RECOMMENDATIONS_TASK_NAME,
)
from .utils import (
    build_attack_scenarios_names_for_top_recommendations,
    get_design_doc,
    get_policies,
    to_design_doc_recommendations,
    to_policy_design_recommendations,
    update_redis_process_status,
)

CELERY_PRIORITY = 9  # higher is better, 10 is the maximum
LOGGER = logging.getLogger("design_doc")


class FailedToProcess(Exception):
    pass


class DesignDocProcessor(ABC):
    def __init__(  # noqa: PLR0913
        self,
        account_id: AccountIdType,
        source_id: SourceIdType,
        design_doc_id: int,
        origin_id: str,
        headers: CeleryHeaders,
        progress_manager: ProgressManager,
        doc_source_type: DesignDocType,
    ) -> None:
        self._account_id = account_id
        self._design_doc_id = design_doc_id
        self._origin_id = origin_id
        self._celery_headers = headers
        self._source_id = source_id
        self._progress_manager = progress_manager
        self._redis_client = get_redis_for_account(account_id, get_config(), get_config().namespace)
        self._errors: dict[str, str] = {}
        self._doc_source_type = doc_source_type

    @property
    def number_of_steps(self) -> int:
        return 2

    def _get_headers(self, issue_id: str) -> CeleryHeaders:
        headers = self._celery_headers.model_copy()
        headers.issue_id = issue_id
        return headers

    @property
    def design_doc_id(self) -> int:
        return self._design_doc_id

    @property
    def origin_id(self) -> str:
        return self._origin_id

    @property
    def design_doc_type(self) -> DesignDocType:
        return self._doc_source_type

    @abstractmethod
    async def process(self) -> None:
        pass

    async def _update_redis_progress(self) -> None:
        LOGGER.debug("Updating redis progress for design doc id %s", self.design_doc_id)
        await update_redis_process_status(
            job_status=JobStatus.RUNNING,
            current_progress=self._progress_manager.progress,
            redis_client=self._redis_client,
            design_doc_id=self.design_doc_id,
        )

    async def _increase_step_progress_and_update_redis_progress(self, steps: int = 1) -> None:
        await self._progress_manager.increase_step_progress(steps)
        await self._update_redis_progress()

    async def process_policies(self) -> None:
        LOGGER.info("Processing policies for design doc id [%s]", self.design_doc_id)
        await self._generate_policies_recommendations()
        await self._increase_step_progress_and_update_redis_progress()

    async def _store_security_review_result(  # noqa: PLR0913
        self,
        attack_scenarios: list[GenAIAttackScenario],
        summary: str,
        description: str,
        mermaid_diagram: str | None,
        top_recommendations: list[DesignDocTopRecommendation],
        scenario_id_to_recommendations: dict[int, list[DesignDocTopRecommendation]],
        all_attack_scenario_recommendations: list[AttackScenarioRecommendation] | list[AttackScenarioRecommendation],
        dataflow_diagram: str | None,
    ) -> None:
        attack_scenarios_to_store = []
        for attack_scenario in attack_scenarios:
            LOGGER.debug(
                "Recommendations for attack scenario [%s] - [%s]",
                attack_scenario.unique_id,
                len(scenario_id_to_recommendations[attack_scenario.unique_id]),
            )
            attack_scenarios_to_store.append(
                AttackScenario(
                    name=attack_scenario.name,
                    id=attack_scenario.unique_id,
                    markdown=attack_scenario.attack_scenario_markdown,
                    dataflow_diagram=attack_scenario.mermaid_section,
                    recommendations=scenario_id_to_recommendations[attack_scenario.unique_id],
                )
            )

        build_attack_scenarios_names_for_top_recommendations(
            top_recommendations, all_attack_scenario_recommendations, attack_scenarios
        )
        async with get_service_dal_context() as service_dal:
            await service_dal.design_docs_dal.update_design_doc(
                self._account_id,
                self._design_doc_id,
                mermaid_diagram=mermaid_diagram or "graph LR; A[No Diagram generated];",
                summary=summary,
                description=description,
                top_recommendations=top_recommendations,
                attack_scenarios=attack_scenarios_to_store,
                dataflow_diagram=dataflow_diagram,
            )

    def _convert_gen_all_recommendation_to_design_recommendation(
        self,
        rec: AttackScenarioRecommendation | AttackScenarioRecommendation,
    ) -> DesignDocTopRecommendation:
        quotes = []
        if type(rec) is AttackScenarioRecommendation:
            quotes = rec.quote_list.quote or []

        return DesignDocTopRecommendation(
            id=rec.id,  # type: ignore[arg-type]
            title=rec.title,
            description=rec.description,
            evidence=rec.evidence,
            quotes=quotes,
            references=[],
            component="",
            concern=rec.concern,
            attack_scenarios_names=[],
        )

    def build_scenario_to_recommendation_mapping(
        self,
        all_recommendations: Sequence[AttackScenarioRecommendation | AttackScenarioRecommendation],
    ) -> dict[int, list[DesignDocTopRecommendation]]:
        scenario_id_to_recommendations: dict[int, list[DesignDocTopRecommendation]] = {}
        for rec in all_recommendations:
            if rec.attack_scenario_id not in scenario_id_to_recommendations:
                scenario_id_to_recommendations[rec.attack_scenario_id] = []

            design_rec = self._convert_gen_all_recommendation_to_design_recommendation(rec)
            scenario_id_to_recommendations[rec.attack_scenario_id].append(design_rec)

        return scenario_id_to_recommendations

    async def _get_top_recommendations_policy(
        self, recommendations: list[DesignDocTopRecommendation]
    ) -> PolicyRecommendationsTaskOutput:
        if not (policies := await get_policies(self._account_id)):
            LOGGER.warning("No policies found for account %s, skipping policy recommendations", self._account_id)
            return PolicyRecommendationsTaskOutput()
        input_args = PolicyRecommendationsTaskInput(
            recommendations=[
                Recommendation(id=rec.id, title=rec.title, description=rec.description) for rec in recommendations
            ],
            policy_documents=policies,
        )
        policy_recommendation_result = await celery_manager_instance.send_and_wait_for_result(
            input_args=input_args.to_dict(),
            headers=self._get_headers(self._origin_id),
            task_name=GENAI_POLICY_RECOMMENDATIONS_TASK_NAME,
            queue=GENAI_PIPELINES_QUEUE,
            priority=CELERY_PRIORITY,
        )
        return PolicyRecommendationsTaskOutput(**policy_recommendation_result)

    async def _generate_policies_recommendations(self) -> None:
        async with get_service_dal_context() as service_dal:
            design_doc = await service_dal.design_docs_dal.get_design_docs_by_id(
                account_id=self._account_id, doc_id=self._design_doc_id
            )
            if design_doc.top_recommendations is None:
                LOGGER.debug(
                    "No top recommendations found for design doc %s, cannot generate policy recommendations",
                    self._design_doc_id,
                )
                return

            policy_recommendation_output = await self._get_top_recommendations_policy(design_doc.top_recommendations)
            ai_policy_recommendations = (
                policy_recommendation_output.results.recommendations if policy_recommendation_output.results else None
            )
            if not ai_policy_recommendations:
                LOGGER.warning("No policy recommendations found for design doc %s", self._design_doc_id)
                return
            LOGGER.info("Updating policy top recommendations for Design Doc %s", self._origin_id)
            policy_recommendations = to_policy_design_recommendations(
                ai_policy_recommendations,
                design_doc.top_recommendations,
            )

            await service_dal.design_docs_dal.update_design_doc(
                self._account_id, self._design_doc_id, policy_recommendations=policy_recommendations
            )


class DesignDocProcessorContainer(DesignDocProcessor):
    def __init__(  # noqa: PLR0913
        self,
        account_id: AccountIdType,
        source_id: SourceIdType,
        design_doc_id: int,
        origin_id: str,
        headers: CeleryHeaders,
        progress_manager: ProgressManager,
        children: list[RatLogicIssue],
        case: ExternalCase,
        doc_source_type: DesignDocType,
    ) -> None:
        self._children = children
        self._case = case
        super().__init__(account_id, source_id, design_doc_id, origin_id, headers, progress_manager, doc_source_type)

    async def process(self) -> None:
        try:
            LOGGER.info("Generating container security review for design doc id [%s]", self.design_doc_id)
            await self._update_redis_progress()

            async with get_service_dal_context() as service_dal:
                design_doc = await service_dal.design_docs_dal.get_design_docs_by_id(
                    account_id=self._account_id, doc_id=self._design_doc_id
                )
            await self._generate_ai_container_review(design_doc.case_id)  # type: ignore[arg-type]
            await self._increase_step_progress_and_update_redis_progress()

            await self.process_policies()
        except:
            LOGGER.exception("Failed to generate design doc for %s", self.design_doc_id)
            raise

    async def _generate_ai_container_review(self, case_id: int) -> None:
        LOGGER.info("Generating security review container with case id %s", case_id)
        LOGGER.debug("Input args for container design doc: %s", [child.id for child in self._children])
        gen_ai_issues: list[GenAIssue] = [GenAIssue.model_validate(child.to_dict()) for child in self._children]

        agent_context = await AgentContext(self._account_id, self._design_doc_id).get_context()
        if agent_context:
            LOGGER.debug("Agent context total messages: %s", len(agent_context))
        input_args = ContainerTaskInput(container_issues=gen_ai_issues, user_messages=agent_context)

        ai_output = await celery_manager_instance.send_and_wait_for_result(
            input_args=input_args.to_dict(),
            headers=self._get_headers(self._origin_id),
            task_name=GENAI_CONTAINER_PROCESSING_TASK_NAME,
            queue=GENAI_PIPELINES_QUEUE,
        )

        security_review_output = ContainerTaskOutput(**ai_output)
        LOGGER.info("Security review container output: %s", security_review_output.model_dump())

        security_review_result = security_review_output.results

        if not security_review_result:
            raise FailedToProcess("Failed to generate security review doc for %s", case_id)

        attack_scenario_result = security_review_result.attack_scenarios_result.list_attack_scenarios.attack_scenario

        scenario_id_to_recommendations = self.build_scenario_to_recommendation_mapping(
            security_review_result.attack_scenarios_result.all_recommendations
        )

        design_doc_recommendations = to_design_doc_recommendations(security_review_result.recommendations)
        await self._store_security_review_result(
            attack_scenarios=attack_scenario_result,  # type: ignore[arg-type]
            summary=self._case.issue_analysis.short_ai_summary,
            description="",  # TODO remove it later
            mermaid_diagram=security_review_result.mermaid_diagram,
            top_recommendations=design_doc_recommendations,
            scenario_id_to_recommendations=scenario_id_to_recommendations,
            all_attack_scenario_recommendations=security_review_result.attack_scenarios_result.all_recommendations,
            dataflow_diagram=security_review_result.attack_scenarios_result.dataflow_diagram,
        )


class DesignDocProcessorGeneral(DesignDocProcessor):
    async def process(self) -> None:
        try:
            LOGGER.info("Generating general security review for design doc id [%s]", self.design_doc_id)
            await self._update_redis_progress()

            await self._generate_ai_review_doc()
            await self._increase_step_progress_and_update_redis_progress()

            await self.process_policies()
        except:
            LOGGER.exception("Failed to generate design doc for %s", self.design_doc_id)
            raise

    async def _generate_ai_review_doc(self) -> None:  # Change name
        LOGGER.info("Generating security review design doc for %s", self.design_doc_id)

        document = await get_design_doc(self._account_id, self._source_id, self._origin_id)
        agent_context = await AgentContext(self._account_id, self._design_doc_id).get_context()
        if agent_context:
            LOGGER.debug("Agent context total messages: %s", len(agent_context))
        input_args = DesignDocumentProcessTaskInput(design_document=document, user_messages=agent_context)

        design_doc_result = await celery_manager_instance.send_and_wait_for_result(
            input_args=input_args.to_dict(),
            headers=self._get_headers(self._origin_id),
            task_name=GENAI_DESIGN_DOCUMENT_PROCESSING_TASK_NAME,
            queue=GENAI_PIPELINES_QUEUE,
        )
        design_doc_output = DesignDocumentProcessTaskOutput(**design_doc_result)

        LOGGER.info("Design docs output: %s", design_doc_output.model_dump())
        security_review_result = design_doc_output.results

        if not security_review_result:
            raise FailedToProcess("Failed to generate security review doc for %s", self.design_doc_id)

        attack_scenario_result = security_review_result.attack_scenarios_result.list_attack_scenarios.attack_scenario

        scenario_id_to_recommendations = self.build_scenario_to_recommendation_mapping(
            security_review_result.attack_scenarios_result.all_recommendations
        )

        summary_result = security_review_result.summary
        top_recommendations = to_design_doc_recommendations(security_review_result.recommendations)
        await self._store_security_review_result(
            attack_scenarios=attack_scenario_result,  # type: ignore[arg-type]
            summary=summary_result.description,
            description=summary_result.description,
            mermaid_diagram=security_review_result.mermaid_diagram,
            top_recommendations=top_recommendations,
            scenario_id_to_recommendations=scenario_id_to_recommendations,
            all_attack_scenario_recommendations=security_review_result.attack_scenarios_result.all_recommendations,
            dataflow_diagram=security_review_result.attack_scenarios_result.dataflow_diagram,
        )
