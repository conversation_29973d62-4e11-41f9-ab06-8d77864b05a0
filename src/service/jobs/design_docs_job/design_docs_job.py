from __future__ import annotations

import asyncio
import logging
from datetime import datetime
from typing import cast

from prime_events import EventMessage, EventNotifier, EventStatus
from prime_fetcher_service_client import SourceType as FetcherSourceType
from prime_jobs import JobStatus
from prime_shared.common_types import AccountIdType
from prime_shared.consts import AUTO_USER
from prime_utils import AsyncRateLimit
from pydantic.dataclasses import dataclass

from service.config import get_config
from service.db import DesignDocsTable, ServiceDAL
from service.job_type import JobType
from service.logic import (
    DESIGN_DOC_EVENT_TITLE,
    DESIGN_DOC_LINK_SEPERATOR,
    merge_urls,
    save_design_doc_by_url,
)
from service.services_clients import ServicesClients

from ...models.design_docs import DesignDocType
from ..base_job_logic import GenAIBase<PERSON>ob
from ..base_job_logic.gen_ai_celery import celery_manager_instance
from .design_doc_processor import DesignDocProcessor, DesignDocProcessor<PERSON><PERSON>r, DesignDocProcessorGeneral
from .design_docs_reporter import DesignDocsMetricsReporter
from .models import DesignDocProcessorResult, DesignDocsJobReport, JobStatisticsResults
from .notify_design_review_complete import NotifyDesignReviewCompleted
from .utils import update_redis_process_status

TOTAL_RUNNING_TASKS_PROGRESS = 90  # percentage
LOGGER = logging.getLogger("design_docs_generation")


@dataclass
class DesignDocsError:
    issue_id: str
    error: str


class DesignDocsJob(GenAIBaseJob):
    def __init__(
        self,
        account_id: AccountIdType,
        job_id: int,
        design_doc_ids: list[int] | None,
        force: bool,
        service_dal: ServiceDAL,
        source_id: int,  # NOTE: until we use static source_id, we need to pass it here
    ) -> None:
        super().__init__(account_id, source_id, job_id, service_dal)
        self._design_doc_ids = design_doc_ids
        self._design_docs_errors: dict[str, DesignDocsError] = {}
        self._job_statistics_results = JobStatisticsResults()
        self._force = force

    @property
    def job_type(self) -> str:
        return JobType.DESIGN_DOCS.value

    async def _needs_update(self, doc: DesignDocsTable) -> bool:
        if not doc.summary:
            return True  # first run
        if not doc.url:
            return False
        if DESIGN_DOC_LINK_SEPERATOR in doc.url:
            LOGGER.info("we are dealing with a combined url")
            urls = doc.url.split(DESIGN_DOC_LINK_SEPERATOR)
        else:
            urls = [doc.url]
        tasks = [self._is_url_updated(url, doc.updated_at) for url in urls]
        return any(cast(list[bool], await asyncio.gather(*tasks)))

    async def _get_design_doc_to_process(self) -> list[int]:
        all_docs = await self._service_dal.design_docs_dal.get_design_docs(account_id=self._account_id)
        if self._design_doc_ids:
            docs_to_process = [doc for doc in all_docs if doc.id in self._design_doc_ids]
        else:
            docs_to_process = [doc for doc in all_docs if await self._needs_update(doc)]
        await self._download_urls(docs_to_process)
        needs_update = [doc.id for doc in docs_to_process]
        LOGGER.info("Design docs to process: %s", needs_update)
        return needs_update

    async def _process_design_docs(self) -> list[DesignDocProcessorResult]:
        try:
            await self._progress_manager.increase_step_progress(steps=5)

            self._design_doc_ids = await self._get_design_doc_to_process()
            await self.update_redis_status_on_start()
            processors = [await self._prepare_processor(doc_id) for doc_id in self._design_doc_ids]
            if not processors:
                LOGGER.info("No design docs to process")
                return []

            total_steps = sum(processor.number_of_steps for processor in processors)
            self._setup_progress_step(TOTAL_RUNNING_TASKS_PROGRESS / total_steps)

            gather = AsyncRateLimit(get_config().max_workers)
            tasks = [processor.process() for processor in processors]
            results = await gather.gather_tasks(tasks, return_exceptions=True)
            generated_design_docs = [
                DesignDocProcessorResult(
                    design_doc_id=processor.design_doc_id,
                    design_doc_name=processor.origin_id,
                    design_doc_type=processor.design_doc_type,
                )
                for processor, result in zip(processors, results, strict=False)
                if not isinstance(result, Exception)
            ]
        except Exception:
            LOGGER.exception("Failed to generate design docs")
            raise
        error_issues = [error.issue_id for error in self._design_docs_errors.values()]
        self._job_statistics_results.total_issues_with_errors = len(set(error_issues))
        return generated_design_docs

    async def _prepare_processor(self, design_doc_id: int) -> DesignDocProcessor:
        headers = self._get_celery_headers(str(design_doc_id), "", {})
        design_doc = await self._service_dal.design_docs_dal.get_design_docs_by_id(self._account_id, design_doc_id)
        origin_id = cast(str, design_doc.file_origin_id)

        if design_doc.doc_source_type == DesignDocType.CONTAINER:
            case = await ServicesClients().cases_api().get_case_by_id(self._account_id, cast(int, design_doc.case_id))
            ai_issues = await ServicesClients.issues_api().get_container_children_data(
                account_id=self._account_id,
                case_id=cast(int, design_doc.case_id),
            )
            return DesignDocProcessorContainer(
                account_id=self._account_id,
                source_id=self._source_id,
                design_doc_id=design_doc_id,
                origin_id=design_doc.title,  # origin id is identifier for AI header update
                headers=headers,
                progress_manager=self._progress_manager,
                children=ai_issues,
                case=case,
                doc_source_type=design_doc.doc_source_type,
            )
        else:
            return DesignDocProcessorGeneral(
                account_id=self._account_id,
                source_id=self._source_id,
                design_doc_id=design_doc_id,
                origin_id=origin_id or design_doc.title,
                headers=headers,
                progress_manager=self._progress_manager,
                doc_source_type=design_doc.doc_source_type,
            )

    async def _run(self) -> None:
        LOGGER.info("Running design docs generation job")
        design_docs = await self._process_design_docs()
        await self._notify_design_doc_flow_completion(True, design_docs)
        celery_manager_instance._celery_app._app.close()
        LOGGER.info("Design docs generation job finished")
        if self._design_docs_errors:
            total_errors = len(self._design_docs_errors)
            keys = "\n".join(self._design_docs_errors.keys())
            LOGGER.warning("Design docs generation errors(%s): %s", total_errors, keys)

    async def report(self, execution_status: bool) -> None:
        reporter = DesignDocsMetricsReporter(
            account_id=self._account_id, source_id=self._source_id, job_id=self.get_job_id()
        )
        design_docs_report = DesignDocsJobReport(
            errors=self._job_statistics_results.total_issues_with_errors,
            duration_in_seconds=self.duration,
            execution_status=execution_status,
            new_cases_created=self._job_statistics_results.total_new_cases_created,
        )
        LOGGER.info("Reporting job metrics %s", design_docs_report)
        reporter.report_job_metrics(design_docs_report)

    async def _notify_design_doc_flow_completion(
        self, execution_status: bool, design_doc_results: list[DesignDocProcessorResult]
    ) -> None:
        event_notifier = EventNotifier(self._redis_client, self.created_by)
        for result in design_doc_results:
            msg = "successfully" if execution_status else "with errors"
            status = EventStatus.COMPLETED if execution_status else EventStatus.FAILED
            try:
                await event_notifier.notify(
                    status=status,
                    message=EventMessage(
                        title=DESIGN_DOC_EVENT_TITLE,
                        description=f"Design Documents Flow Completed {msg} for: {result.design_doc_name}",
                    ),
                    action="design_doc_flow_completed",
                    progress=100,
                )
            except Exception:
                LOGGER.exception("Failed to notify design doc flow completion")

            if (
                self.created_by != AUTO_USER and result.design_doc_type != DesignDocType.REFERENCE
            ) and execution_status:
                await self._send_user_notification(self.created_by, self._account_id, result.design_doc_id)

    @staticmethod
    async def _send_user_notification(user: str, account_id: AccountIdType, design_doc_id: int) -> None:
        notifier = NotifyDesignReviewCompleted(account_id=account_id, user=user, document_id=design_doc_id)
        await notifier.notify()

    async def _is_url_updated(self, url: str, updated_at: datetime | None) -> bool:
        download_api = ServicesClients.download_api()
        source_info = await download_api.get_url_source_info(self._account_id, url)
        file_source_id = source_info.source_id
        if source_info.source_type == FetcherSourceType.GOOGLE:
            file_info = await download_api.gdrive_url_metadata(self._account_id, file_source_id, url)
        else:
            file_info = await download_api.confluence_url_metadata(self._account_id, file_source_id, url)
        return updated_at is None or file_info.modified_time > updated_at

    async def _download_urls(self, design_docs: list[DesignDocsTable]) -> None:
        save_tasks = []
        for doc in design_docs:
            if not doc.url:
                continue
            if DESIGN_DOC_LINK_SEPERATOR in doc.url:
                save_tasks.append(merge_urls(self._account_id, doc.url.split(DESIGN_DOC_LINK_SEPERATOR)))
            else:
                save_tasks.append(save_design_doc_by_url(self._account_id, doc.url))
        # TODO: improve this, maybe remove urls that failed from processing
        for result in await asyncio.gather(*save_tasks, return_exceptions=True):
            if isinstance(result, Exception):
                LOGGER.error("Failed to save design doc by URL: %s", result)
                continue

    async def update_redis_status_on_finish(self, job_status: JobStatus) -> None:
        LOGGER.info("Updating Redis design docs status on finish")
        if not self._design_doc_ids:
            LOGGER.warning("No design doc IDs to update redis status on finish")
            return

        for design_doc_id in self._design_doc_ids:
            await update_redis_process_status(
                job_status=job_status,
                current_progress=100,
                redis_client=self._redis_client,
                design_doc_id=design_doc_id,
            )

    async def update_redis_status_on_start(self) -> None:
        LOGGER.info("Updating Redis design docs status on start")
        if not self._design_doc_ids:
            LOGGER.warning("No design doc IDs to update status for start")
            return

        if self._design_doc_ids:
            for design_doc_id in self._design_doc_ids:
                await update_redis_process_status(
                    job_status=JobStatus.RUNNING,
                    current_progress=self._progress_manager.progress,
                    redis_client=self._redis_client,
                    design_doc_id=design_doc_id,
                )
        else:
            LOGGER.warning("No design doc IDs to update status for finish")
