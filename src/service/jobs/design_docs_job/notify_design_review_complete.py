from __future__ import annotations

import logging

from prime_notification_service_client import (
    DesignReviewCompletedMailNotificationRequest,
    MailNotifyReq,
    OutputNotificationType,
)
from prime_shared.common_types import AccountIdType
from prime_utils.monitoring import report_exception
from pydantic import BaseModel

from service.services_clients import ServicesClients

LOGGER = logging.getLogger(__name__)


class NotifyDesignReviewCompleted(BaseModel):
    account_id: AccountIdType
    user: str
    document_id: int

    async def _send_mail_notification(self) -> None:
        req = DesignReviewCompletedMailNotificationRequest(
            user=self.user,
            mail_recipients=[self.user],
            notification_type=OutputNotificationType.DESIGN_REVIEW_COMPLETED.value,
            document_id=str(self.document_id),
        )
        LOGGER.info("Sending mail notification %s", req)
        await ServicesClients.notification_api().notify_mail(
            account_id=self.account_id, mail_notify_req=MailNotifyReq(actual_instance=req)
        )

    async def notify(self) -> None:
        try:
            await self._send_mail_notification()
        except Exception as e:
            LOGGER.exception(
                "Failed to send design review completed notification to user %s, doc id %s", self.user, self.document_id
            )
            report_exception(e)
