import logging

from prime_shared.common_types import AccountIdType
from prime_source_service_client import SourceType
from prime_utils import alru_cache

from service.db import get_service_dal_context
from service.services_clients import ServicesClients

from ..run_job_main import run_main
from .design_docs_job import DesignDocsJob
from .models import DesignDocsJobArgs

LOGGER = logging.getLogger("design-docs-generation")


@alru_cache
async def get_design_docs_source_id(account_id: AccountIdType) -> int:
    sources = await ServicesClients.source_api().get_sources(account_id, source_type=SourceType.DESIGNDOCS)
    return sources[0].id


async def main() -> None:
    job_vars = DesignDocsJobArgs()
    LOGGER.info("Running Design Docs Generation Job with args: %s", job_vars)
    source_id = await get_design_docs_source_id(job_vars.account_id)
    async with get_service_dal_context() as service_dal:
        job_logic = DesignDocsJob(
            account_id=job_vars.account_id,
            job_id=job_vars.job_id,
            design_doc_ids=job_vars.design_doc_ids,
            force=job_vars.force,
            service_dal=service_dal,
            source_id=source_id,
        )
        await job_logic.start()


if __name__ == "__main__":
    run_main(main)
