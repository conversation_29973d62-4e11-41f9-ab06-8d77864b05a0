import os
import time
from typing import Any

from datadog import initialize, statsd
from prime_shared.common_types import AccountIdType, SourceIdType

from service.config import get_config
from service.job_type import JobType

from .models import DesignDocsJobReport

DD_METRICS_PREFIX = "prime.job.design_docs"


class DesignDocsMetricsReporter:
    def __init__(self, account_id: AccountIdType, source_id: SourceIdType, job_id: int):
        self.account_id = account_id
        self.job_id = job_id
        self.source_id = source_id

        self._base_tags = frozenset(
            [
                f"account_id:{self.account_id}",
                f"source_id:{self.source_id}",
                f"job_id:{self.job_id}",
                f"namespace:{get_config().namespace}",
                f"job_type:{JobType.DESIGN_DOCS.value}",
            ]
        )
        # Datadog automatically picks up API key from DD_API_KEY environment variable
        statsd_host = os.getenv("DD_AGENT_HOST", "localhost")
        initialize(statsd_host=statsd_host)

    def report_job_metrics(
        self,
        design_docs_report: DesignDocsJobReport,
        additional_tags: dict[str, Any] | None = None,
    ) -> None:
        base_tags = list(self._base_tags)

        # Add any additional tags
        if additional_tags:
            base_tags.extend([f"{k}:{v}" for k, v in additional_tags.items()])
        finished_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time()))
        base_tags.append(f"job_time:{finished_time}")
        base_tags.append(f"job_state:{design_docs_report.job_state.value}")

        metrics = {
            f"{DD_METRICS_PREFIX}.new_cases_created": design_docs_report.new_cases_created,
            f"{DD_METRICS_PREFIX}.errors.total": design_docs_report.errors,
        }

        for metric, value in metrics.items():
            statsd.gauge(metric, value, tags=base_tags)

        statsd.timing(f"{DD_METRICS_PREFIX}.duration_seconds", design_docs_report.duration_in_seconds, tags=base_tags)

        if design_docs_report.execution_status is False:
            statsd.event(
                title="Design Docs Job Execution Failed",
                message=f"Job failed for account {self.account_id}, source {self.source_id}, job {self.job_id}",
                tags=base_tags,
                alert_type="error",
                source_type_name="design_docs_job",
            )
