from __future__ import annotations

import json
import logging
from typing import Any

from prime_gen_ai_service_client import (
    <PERSON>Inner,
    MessageContent,
    MessageDocument,
)
from prime_shared.common_types import AccountIdType
from pydantic_ai.messages import (
    BinaryContent,
    ModelMessage,
    ModelMessagesTypeAdapter,
    ModelResponse,
    UserPromptPart,
)

from service.db import get_service_dal_context
from service.errors.errors import NoConversationFoundError
from service.services_clients import ServicesClients

LOGGER = logging.getLogger("design_doc_agent_context")


class AgentContext:
    def __init__(self, account_id: AccountIdType, design_doc_id: int):
        self._account_id = account_id
        self._design_doc_id = design_doc_id

    async def get_context(self) -> list[MessageContent] | None:
        LOGGER.info(
            "Getting agent conversation context for account_id: %s, design_doc_id: %d",
            self._account_id,
            self._design_doc_id,
        )
        try:
            conversation = await self._fetch_conversation_archive()
            if conversation is None:
                return None

            return await self._process_conversation_messages(conversation)
        except Exception:
            LOGGER.exception("Error getting agent conversation context")
            return None

    async def _fetch_conversation_archive(self) -> str | None:
        async with get_service_dal_context() as service_dal:
            design_doc = await service_dal.design_docs_dal.get_design_docs_by_id(
                account_id=self._account_id, doc_id=self._design_doc_id
            )
            LOGGER.debug("Retrieved design doc with agent_conversation_id: %s", design_doc.agent_conversation_id)

            if not design_doc.agent_conversation_id:
                return None

            return await self._get_conversation_from_archive(design_doc.agent_conversation_id)

    async def _get_conversation_from_archive(self, conversation_id: int) -> str | None:
        LOGGER.info("Fetching conversation archive for conversation_id: %s", conversation_id)
        conversation = await ServicesClients.llm_archive_api().get_conversation_archive(
            account_id=self._account_id, conversation_id=conversation_id
        )

        if not conversation:
            LOGGER.error(
                "No conversation found for account_id: %s, design_doc_id: %d, conversation_id: %s",
                self._account_id,
                self._design_doc_id,
                conversation_id,
            )
            raise NoConversationFoundError(self._account_id, self._design_doc_id, conversation_id)
        LOGGER.debug("Conversation archive retrieved successfully")

        return conversation

    async def _process_conversation_messages(self, conversation: str) -> list[MessageContent] | None:
        LOGGER.debug("Parsing conversation JSON and processing messages")
        text = json.loads(conversation)
        LOGGER.debug("Found %d messages in conversation", len(text))

        msg_content: list[MessageContent] = []
        for i, message in enumerate(text):
            LOGGER.debug("Processing message %d", i + 1)
            content = self._extract_message_content(message)
            if content:
                msg_content.append(content)
                LOGGER.debug("Added content from message %d", i + 1)
            else:
                LOGGER.debug("No content extracted from message %d", i + 1)

        LOGGER.info("Successfully processed conversation context with %d message contents", len(msg_content))
        if not msg_content:
            return None
        return msg_content

    def _extract_message_content(self, message: dict[str, Any]) -> MessageContent | None:
        model = ModelMessagesTypeAdapter.validate_python([message])
        result = self.get_message_content_for_flows(model[0])
        if not result:
            return None
        return result

    @staticmethod
    def get_message_content_for_flows(message: ModelMessage) -> MessageContent | None:
        LOGGER.debug("Processing ModelMessage with %d parts", len(message.parts))
        if isinstance(message, ModelResponse):
            # currently we dont need LLM responses
            return None

        content_list: list[ContentInner] = []

        for part in message.parts:
            LOGGER.debug("Processing part  type: %s", type(part).__name__)

            if not isinstance(part, UserPromptPart):
                # system prompts are not needed for the agent context
                continue

            # Handle string content directly
            if isinstance(part.content, str):
                LOGGER.debug("Found string content with length: %d", len(part.content))
                content_list.append(ContentInner(part.content))
                continue

            # Handle list of content parts
            LOGGER.debug("Processing list content with %d items", len(part.content))
            for j, content_part in enumerate(part.content):
                LOGGER.debug("Processing content part %d of type: %s", j + 1, type(content_part).__name__)

                if isinstance(content_part, str):
                    LOGGER.debug("Found string content part with length: %d", len(content_part))
                    content_list.append(ContentInner(content_part))
                elif isinstance(content_part, BinaryContent):
                    media_type = content_part.media_type
                    file_format = media_type.split("/")[-1]
                    data_size = len(content_part.data)
                    LOGGER.debug(
                        "Found binary content: media_type=%s, format=%s, size=%d bytes",
                        media_type,
                        file_format,
                        data_size,
                    )
                    content_list.append(
                        ContentInner(
                            MessageDocument(
                                file_name="",
                                file_bytes=content_part.data,
                                file_format=file_format,
                            )
                        )
                    )

        if not content_list:
            LOGGER.debug("No extractable content found in message")
            return None

        return MessageContent(content=content_list)
