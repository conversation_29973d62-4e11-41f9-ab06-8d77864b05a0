import json

from prime_shared.common_types import AccountIdType
from pydantic import BaseModel, computed_field
from pydantic.dataclasses import dataclass
from pydantic_settings import BaseSettings

from service.models.design_docs import DesignDocType
from service.models.jobs import JobState


class DesignDocsJobArgs(BaseSettings):
    account_id: AccountIdType
    job_id: int
    design_doc_ids: list[int] | None = None
    force: bool


class JobStatisticsResults(BaseModel):
    total_issues_with_errors: int = 0
    total_new_cases_created: int = 0
    total_issues_finished_summary: int = 0


class DesignDocsJobReport(BaseModel):
    new_cases_created: int

    execution_status: bool
    duration_in_seconds: int = 0

    errors: int

    def as_str_report(self) -> str:
        return json.dumps({key.replace("_", " ").title() for key, value in self.model_dump().items()})

    @computed_field  # type: ignore[prop-decorator]
    @property
    def job_state(self) -> JobState:
        if self.execution_status is False:
            return JobState.TERMINATED
        if self.errors > 0:
            return JobState.COMPLETED_WITH_ERRORS
        return JobState.COMPLETED


@dataclass
class DesignDocProcessorResult:
    design_doc_id: int
    design_doc_name: str
    design_doc_type: DesignDocType
