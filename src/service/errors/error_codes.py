# pylint: disable=invalid-name
import enum


class ErrorCode(enum.Enum):
    # E0001 - E0999 Are reserved for general / common errors (see primesec_service_kit. error_codes_mapping)
    AIGenerationTimeoutError = "E2511-AIGenerationTimeoutError"

    AIGenerationError = "E2510-AIGenerationError"

    DesignDocDuplicateError = "E2601-DesignDocDuplicateError"
    DesignDocAttachContextError = "E2602-DesignDocAttachContextError"
    DesignDocNotFoundError = "E2603-DesignDocNotFoundError"
    DesignDocUpdateNotAllowed = "E2604-DesignDocUpdateNotAllowed"
    NoConversationFoundError = "E2605-NoConversationFoundError"
