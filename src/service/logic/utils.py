import logging

from prime_jobs import Job, JobScheduler
from prime_shared.common_types import AccountIdType

from service.models.jobs import JobCreateArg

LOGGER = logging.getLogger("utils")


async def add_job_logic(account_id: AccountIdType, job_create_args: JobCreateArg, scheduler: JobScheduler) -> Job:
    job_args = job_create_args.model_dump(exclude={"job", "created_by"})
    new_job = await scheduler.add_job(
        account_id=account_id,
        job_args=job_args,
        created_by=job_create_args.created_by,
        job_type=str(job_create_args.job.value),
    )
    return new_job
