import asyncio
import json
import logging
from dataclasses import dataclass
from datetime import UTC, datetime
from io import BytesIO
from urllib.parse import quote

from prime_events import EventMessage, EventNotifier, EventStatus
from prime_fetcher_service_client import UrlMetadata
from prime_file_manager_service_client import DocumentType, FileInfo, FileOriginRequest, FileUploadMetadataRequest
from prime_jobs import JobScheduler
from prime_redis_utils import AsyncPrefixRedisClient
from prime_shared.common_types import AccountIdType
from prime_shared.common_values import FETCHER_METADATA_HEADER
from prime_source_service_client import SourceModel, SourceType
from prime_utils import zip_data
from pypdf import PdfWriter

from service.db import DesignDocsTable
from service.errors.errors import DesignDocDuplicateError
from service.logic.utils import add_job_logic
from service.models import JobDesignDocsCreateArgs
from service.services_clients import ServicesClients

LOGGER = logging.getLogger("design-docs")
DESIGN_DOC_EVENT_TITLE = "Design Doc Flow"
DESIGN_DOC_LINK_SEPERATOR = ";"


@dataclass
class URLData:
    file_metadata: UrlMetadata
    file_data: bytes


def _sanitize_filename(filename: str) -> str:
    # Remove any path components and get just the filename
    filename = filename.split("/")[-1]
    # URL encode the filename, but preserve some safe characters
    return quote(filename, safe="-_.")


@dataclass
class DesignDocUploadMetadata:
    name: str
    origin_id: str
    url: str


async def create_design_doc_job(
    docs: list[DesignDocsTable],
    account_id: AccountIdType,
    created_by: str,
    scheduler: JobScheduler,
    redis_client: AsyncPrefixRedisClient,
) -> None:
    LOGGER.info("Creating Design Doc Job for account %s with %d docs", account_id, len(docs))
    job_args = JobDesignDocsCreateArgs(created_by=created_by, design_doc_ids=[doc.id for doc in docs])
    await add_job_logic(account_id, job_args, scheduler)
    event_notifier = EventNotifier(redis_client, created_by)
    await event_notifier.notify(
        EventStatus.IN_PROGRESS,
        EventMessage(title=DESIGN_DOC_EVENT_TITLE, description="Starting design doc generation"),
        progress=10,
    )


async def merge_urls(account_id: AccountIdType, urls: list[str]) -> DesignDocUploadMetadata:
    LOGGER.info("Merging Design Doc URLs: %s", urls)
    tasks = [_download_url(account_id, url) for url in urls]
    all_docs: list[URLData] = await asyncio.gather(*tasks)
    design_doc_name = f"{all_docs[0].file_metadata.name} (+{len(all_docs) - 1})"
    filename = _sanitize_filename(DESIGN_DOC_LINK_SEPERATOR.join(doc.file_metadata.name for doc in all_docs))
    file_data = merge_pdfs([url_data.file_data for url_data in all_docs])
    origin_id = await upload_design_doc(account_id, file_data, filename)
    return DesignDocUploadMetadata(name=design_doc_name, origin_id=origin_id, url=DESIGN_DOC_LINK_SEPERATOR.join(urls))


async def save_design_doc_by_url(account_id: AccountIdType, url: str) -> DesignDocUploadMetadata:
    url_data = await _download_url(account_id, url)
    design_doc_name = url_data.file_metadata.name
    filename = _sanitize_filename(url_data.file_metadata.name)
    origin_id = await upload_design_doc(account_id, url_data.file_data, filename)
    return DesignDocUploadMetadata(name=design_doc_name, origin_id=origin_id, url=url)


async def upload_design_doc(
    account_id: AccountIdType, design_doc_data: bytes, design_doc_name: str, *, allow_update: bool = False
) -> str:
    LOGGER.info("Uploading Design Doc: %s if size %s KB", design_doc_name, len(design_doc_data) / 1000)
    source = await _get_design_docs_source(account_id)
    origin_id = design_doc_name
    files_info = await ServicesClients.files_api().get_files_info(
        account_id=account_id, source_id=source.id, file_origin_request=FileOriginRequest(file_names=[origin_id])
    )
    if files_info.results and not allow_update:
        raise DesignDocDuplicateError([design_doc_name])
    file_upload_metadata_request = FileUploadMetadataRequest(
        origin_id=origin_id,
        source_id=source.id,
        document_type=DocumentType.DESIGN_DOC,
        timestamp=datetime.now(tz=UTC),
        downloadable_link=f"https://primesec.ai/{origin_id}",
        domain="primesec.ai",
    )
    files = [file_upload_metadata_request.model_dump_json().encode(), design_doc_data]
    zip_bytes = zip_data({str(i): data for i, data in enumerate(files)})
    await ServicesClients.files_api().upload_zip_file(account_id, zip_bytes, _content_type="application/zip")
    return origin_id


def merge_pdfs(pdfs_data: list[bytes]) -> bytes:
    pdf_writer = PdfWriter()
    for pdf_bytes in pdfs_data:
        with BytesIO(pdf_bytes) as pdf_input_stream:
            pdf_writer.append(pdf_input_stream)
    with BytesIO() as pdf_output_stream:
        pdf_writer.write(pdf_output_stream)
        value = pdf_output_stream.getvalue()
    pdf_writer.close()
    return value


async def get_existing_files(account_id: AccountIdType, filenames: list[str]) -> list[FileInfo]:
    source = await _get_design_docs_source(account_id)
    files_info = await ServicesClients.files_api().get_files_info(
        account_id=account_id, source_id=source.id, file_origin_request=FileOriginRequest(file_names=filenames)
    )
    return files_info.results


async def _download_url(account_id: AccountIdType, url: str) -> URLData:
    download_api = ServicesClients.download_api()
    file_data_response = await download_api.download_unified_url_with_http_info(account_id, url, pdf_format=True)
    file_data = file_data_response.data
    file_metadata = (file_data_response.headers or {})[FETCHER_METADATA_HEADER]
    return URLData(file_metadata=UrlMetadata(**json.loads(file_metadata)), file_data=file_data)


async def _get_design_docs_source(account_id: AccountIdType) -> SourceModel:
    LOGGER.info("Getting design docs source")
    return (await ServicesClients.source_api().get_sources(account_id, SourceType.DESIGNDOCS))[0]
