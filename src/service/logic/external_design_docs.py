import json
import logging
import re
from datetime import UTC, datetime
from typing import cast

from prime_rat_logic_service_client import ExternalCaseWorkroom
from prime_redis_utils import AsyncPrefixRedisClient
from prime_service_kit.fastapi_utils.pagination import PaginationResponse
from prime_service_kit.shared_types import AccountArgumentType
from prime_shared.common_dataclasses import PaginationArgs
from prime_shared.common_types import AccountIdType
from prime_utils.monitoring import report_exception

from service.db import ServiceDAL
from service.db.tables.design_doc import DesignDocsTable
from service.jobs.design_docs_job.utils import get_redis_processing_progress_percent_key
from service.models.design_docs import (
    DesignDocPreviewDAL,
    DesignDocProcessStatus,
    DesignDocResponse,
    DesignDocType,
    ExtendDesignDocResponse,
)
from service.services_clients import ServicesClients

LOGGER = logging.getLogger("external_design_docs")

case_data_t = dict[int, ExternalCaseWorkroom]
design_doc_t = DesignDocPreviewDAL | DesignDocsTable


async def _get_cases_data(account_id: AccountIdType, case_ids: list[int]) -> case_data_t:
    if not case_ids:
        return {}
    LOGGER.info("Retrieving cases data for account %s, case_ids: %s", account_id, str(case_ids))
    cases_filter = json.dumps({"field": "id", "op": "eq", "value": [str(case_id) for case_id in case_ids]})
    resp = await ServicesClients().cases_api().get_cases_for_account(account_id, f=[cases_filter])
    LOGGER.debug("Cases data retrieved for account %s, %s", account_id, str(resp))
    return {case.case_id: case for case in resp.results}


def mermaid_fix(text: str | None) -> str | None:
    return re.sub(r"(\d+)\.", r"\1\\.", text) if text else None


async def get_external_design_docs(
    *,
    account_id: AccountArgumentType,
    pagination_args: PaginationArgs,
    service_dal: ServiceDAL,
    redis_client: AsyncPrefixRedisClient,
    doc_source_type: DesignDocType | None = None,
) -> PaginationResponse[DesignDocResponse]:
    total = await service_dal.design_docs_dal.get_design_docs_count(account_id, doc_source_type=doc_source_type)
    security_reviews = await service_dal.design_docs_dal.get_design_docs_preview_data(
        account_id=account_id, pagination_args=pagination_args, doc_source_type=doc_source_type
    )
    cases_data = await _get_cases_data(
        account_id, [review.case_id for review in security_reviews if review.case_id is not None]
    )
    external_security_reviews = []
    for review in security_reviews:
        try:
            case_data = cases_data[review.case_id] if review.case_id else None
            external_security_reviews.append(await design_doc_response(review, case_data, redis_client))
        except Exception as e:
            LOGGER.exception("Failed to process security review %s", review.id)
            report_exception(e)
    return PaginationResponse[DesignDocResponse].from_results(external_security_reviews, pagination_args, total)


async def get_extend_external_design_docs(
    *, account_id: AccountArgumentType, doc_id: int, service_dal: ServiceDAL, redis_client: AsyncPrefixRedisClient
) -> ExtendDesignDocResponse:
    design_doc = await service_dal.design_docs_dal.get_design_docs_by_id(account_id, doc_id)
    if design_doc.case_id is None:
        cases_data = None
    else:
        cases_data = (await _get_cases_data(account_id, [design_doc.case_id]))[design_doc.case_id]
    return await _to_extend_external_design_docs(design_doc, cases_data, redis_client)


async def get_processing_status(
    design_doc_id: int, redis_client: AsyncPrefixRedisClient
) -> DesignDocProcessStatus | None:
    security_review_status_binary = await redis_client.get(get_redis_processing_progress_percent_key(design_doc_id))
    design_doc_status = None
    if security_review_status_binary:
        try:
            design_doc_status = DesignDocProcessStatus.model_validate_json(security_review_status_binary)
        except Exception:
            LOGGER.exception("Failed to validate design_doc_status")
    return design_doc_status


async def design_doc_response(
    design_doc: design_doc_t, case_data: ExternalCaseWorkroom | None, redis_client: AsyncPrefixRedisClient
) -> DesignDocResponse:
    design_doc_status = await get_processing_status(design_doc.id, redis_client)
    response = DesignDocResponse(
        id=design_doc.id,
        file_origin_id=design_doc.file_origin_id,
        title=design_doc.title,
        created_by=design_doc.created_by,
        created_at=design_doc.created_at or datetime.now(UTC),
        updated_at=design_doc.updated_at or datetime.now(UTC),
        url=design_doc.url,
        doc_source_type=design_doc.doc_source_type,
        processing_status=design_doc_status,
    )
    if design_doc.doc_source_type in (DesignDocType.CONTAINER, DesignDocType.REFERENCE):
        case_data = cast(ExternalCaseWorkroom, case_data)
        response.issue_id = case_data.issue_id
        response.source_id = case_data.source_id
        response.jira_issue_url = case_data.link
        response.case_id = case_data.case_id
    return response


async def _to_extend_external_design_docs(
    design_doc: DesignDocsTable,
    case_data: ExternalCaseWorkroom | None,
    redis_client: AsyncPrefixRedisClient,
) -> ExtendDesignDocResponse:
    doc = await design_doc_response(design_doc, case_data, redis_client)
    return ExtendDesignDocResponse(
        **doc.model_dump(),
        summary=design_doc.summary or "",
        top_recommendations=design_doc.top_recommendations or [],
        policy_recommendations=design_doc.policy_recommendations or [],
        mermaid_diagram=mermaid_fix(design_doc.mermaid_diagram),
        attack_scenarios=design_doc.attack_scenarios or [],
        dataflow_diagram=mermaid_fix(design_doc.attack_scenario_dataflow_diagram or ""),
    )
