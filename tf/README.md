# Learn Terraform - Provision an EKS Cluster

This repo is a companion repo to the [Provision an EKS Cluster tutorial](https://developer.hashicorp.com/terraform/tutorials/kubernetes/eks), containing
Terraform configuration files to provision an EKS cluster on AWS.

only root account will have access to EKS
two options:
1. 
for each user you want to access, run:
eksctl create iamidentitymapping \
    --cluster <cluster-name> \
    --region <region-name> \
    --arn <user-arn>  \
    --group system:masters \
    --no-duplicate-arns \
    --username admins
2. create a policy EKSAdmin: 
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "eks:*"
            ],
            "Resource": "*"
        }
    ]
}
create a policy AllowAssumeEKSAdminInfo:
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "AllowAssume",
            "Effect": "Allow",
            "Action": [
                "sts:AssumeRole"
            ],
            "Resource": [
                "arn:aws:iam::************:role/eks-admin"
            ]
        }
    ]
}

create a role eks-admin and attched EKSAdmin policy:
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "AWS": "arn:aws:iam::797808101759:user/matan-admin"
            },
            "Action": "sts:AssumeRole"
        }
    ]
}

to assume role run: 
OUT=$(aws sts assume-role --role-arn arn:aws:iam::797808101759:role/aws-admin --role-session-name aaa);\
export AWS_ACCESS_KEY_ID=$(echo $OUT | jq -r '.Credentials''.AccessKeyId');\
export AWS_SECRET_ACCESS_KEY=$(echo $OUT | jq -r '.Credentials''.SecretAccessKey');\
export AWS_SESSION_TOKEN=$(echo $OUT | jq -r '.Credentials''.SessionToken');