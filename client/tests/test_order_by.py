# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_security_review_service_client.models.order_by import OrderBy

class TestOrderBy(unittest.TestCase):
    """OrderBy unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def testOrderBy(self):
        """Test OrderBy"""
        # inst = OrderBy()

if __name__ == '__main__':
    unittest.main()
