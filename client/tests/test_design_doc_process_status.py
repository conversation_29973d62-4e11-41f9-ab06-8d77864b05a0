# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_security_review_service_client.models.design_doc_process_status import DesignDocProcessStatus

class TestDesignDocProcessStatus(unittest.TestCase):
    """DesignDocProcessStatus unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> DesignDocProcessStatus:
        """Test DesignDocProcessStatus
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `DesignDocProcessStatus`
        """
        model = DesignDocProcessStatus()
        if include_optional:
            return DesignDocProcessStatus(
                processing_progress_percent = 1.337,
                job_status = 'SCHEDULED',
                timestamp = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f')
            )
        else:
            return DesignDocProcessStatus(
                processing_progress_percent = 1.337,
                job_status = 'SCHEDULED',
                timestamp = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'),
        )
        """

    def testDesignDocProcessStatus(self):
        """Test DesignDocProcessStatus"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
