# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_security_review_service_client.models.is_alive_response import IsAliveResponse

class TestIsAliveResponse(unittest.TestCase):
    """IsAliveResponse unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> IsAliveResponse:
        """Test IsAliveResponse
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `IsAliveResponse`
        """
        model = IsAliveResponse()
        if include_optional:
            return IsAliveResponse(
                now = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'),
                uptime = '',
                hit_count = 56
            )
        else:
            return IsAliveResponse(
                now = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'),
                uptime = '',
                hit_count = 56,
        )
        """

    def testIsAliveResponse(self):
        """Test IsAliveResponse"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
