# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_security_review_service_client.models.extend_design_doc_response import ExtendDesignDocResponse

class TestExtendDesignDocResponse(unittest.TestCase):
    """ExtendDesignDocResponse unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ExtendDesignDocResponse:
        """Test ExtendDesignDocResponse
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ExtendDesignDocResponse`
        """
        model = ExtendDesignDocResponse()
        if include_optional:
            return ExtendDesignDocResponse(
                id = 56,
                title = '',
                case_id = 56,
                created_by = '',
                created_at = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'),
                updated_at = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'),
                file_origin_id = '',
                url = '',
                issue_id = '',
                source_id = 56,
                doc_source_type = 'original',
                jira_issue_url = '',
                processing_status = prime_security_review_service_client.models.design_doc_process_status.DesignDocProcessStatus(
                    processing_progress_percent = 1.337, 
                    job_status = 'SCHEDULED', 
                    timestamp = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'), ),
                summary = '',
                top_recommendations = [
                    prime_security_review_service_client.models.design_doc_top_recommendation.DesignDocTopRecommendation(
                        id = 56, 
                        title = '', 
                        description = '', 
                        evidence = '', 
                        references = [
                            56
                            ], 
                        component = '', 
                        concern = '', 
                        quotes = [
                            ''
                            ], 
                        attack_scenarios_names = [
                            ''
                            ], )
                    ],
                policy_recommendations = [
                    prime_security_review_service_client.models.design_doc_top_policy_recommendation.DesignDocTopPolicyRecommendation(
                        id = 56, 
                        title = '', 
                        description = '', 
                        evidence = '', 
                        references = [
                            56
                            ], 
                        component = '', 
                        concern = '', 
                        quotes = [
                            ''
                            ], 
                        attack_scenarios_names = [
                            ''
                            ], 
                        policy_quotes = [
                            prime_security_review_service_client.models.design_doc_policy_recommendation_quote.DesignDocPolicyRecommendationQuote(
                                quote_text = '', 
                                quote_source = '', )
                            ], )
                    ],
                mermaid_diagram = '',
                attack_scenarios = [
                    prime_security_review_service_client.models.attack_scenario.AttackScenario(
                        id = 56, 
                        name = '', 
                        markdown = '', 
                        dataflow_diagram = '', 
                        recommendations = [
                            prime_security_review_service_client.models.design_doc_top_recommendation.DesignDocTopRecommendation(
                                id = 56, 
                                title = '', 
                                description = '', 
                                evidence = '', 
                                references = [
                                    56
                                    ], 
                                component = '', 
                                concern = '', 
                                quotes = [
                                    ''
                                    ], 
                                attack_scenarios_names = [
                                    ''
                                    ], )
                            ], )
                    ],
                dataflow_diagram = ''
            )
        else:
            return ExtendDesignDocResponse(
                id = 56,
                title = '',
                created_by = '',
                created_at = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'),
                updated_at = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'),
                file_origin_id = '',
                doc_source_type = 'original',
                summary = '',
        )
        """

    def testExtendDesignDocResponse(self):
        """Test ExtendDesignDocResponse"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
