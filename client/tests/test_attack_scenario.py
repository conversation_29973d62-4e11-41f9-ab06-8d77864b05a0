# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_security_review_service_client.models.attack_scenario import AttackScenario

class TestAttackScenario(unittest.TestCase):
    """AttackScenario unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> AttackScenario:
        """Test AttackScenario
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `AttackScenario`
        """
        model = AttackScenario()
        if include_optional:
            return AttackScenario(
                id = 56,
                name = '',
                markdown = '',
                dataflow_diagram = '',
                recommendations = [
                    prime_security_review_service_client.models.design_doc_top_recommendation.DesignDocTopRecommendation(
                        id = 56, 
                        title = '', 
                        description = '', 
                        evidence = '', 
                        references = [
                            56
                            ], 
                        component = '', 
                        concern = '', 
                        quotes = [
                            ''
                            ], 
                        attack_scenarios_names = [
                            ''
                            ], )
                    ]
            )
        else:
            return AttackScenario(
                id = 56,
                name = '',
                markdown = '',
                dataflow_diagram = '',
                recommendations = [
                    prime_security_review_service_client.models.design_doc_top_recommendation.DesignDocTopRecommendation(
                        id = 56, 
                        title = '', 
                        description = '', 
                        evidence = '', 
                        references = [
                            56
                            ], 
                        component = '', 
                        concern = '', 
                        quotes = [
                            ''
                            ], 
                        attack_scenarios_names = [
                            ''
                            ], )
                    ],
        )
        """

    def testAttackScenario(self):
        """Test AttackScenario"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
