# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_security_review_service_client.models.design_doc_by_multiple_urls_req import DesignDocByMultipleUrlsReq

class TestDesignDocByMultipleUrlsReq(unittest.TestCase):
    """DesignDocByMultipleUrlsReq unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> DesignDocByMultipleUrlsReq:
        """Test DesignDocByMultipleUrlsReq
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `DesignDocByMultipleUrlsReq`
        """
        model = DesignDocByMultipleUrlsReq()
        if include_optional:
            return DesignDocByMultipleUrlsReq(
                urls = [
                    ''
                    ],
                process_as_one = True,
                created_by = ''
            )
        else:
            return DesignDocByMultipleUrlsReq(
                urls = [
                    ''
                    ],
                created_by = '',
        )
        """

    def testDesignDocByMultipleUrlsReq(self):
        """Test DesignDocByMultipleUrlsReq"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
