# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_security_review_service_client.models.attach_context_to_design_doc_req import AttachContextToDesignDocReq

class TestAttachContextToDesignDocReq(unittest.TestCase):
    """AttachContextToDesignDocReq unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> AttachContextToDesignDocReq:
        """Test AttachContextToDesignDocReq
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `AttachContextToDesignDocReq`
        """
        model = AttachContextToDesignDocReq()
        if include_optional:
            return AttachContextToDesignDocReq(
                created_by = '',
                context_type = 'agent',
                context_id = 56
            )
        else:
            return AttachContextToDesignDocReq(
                created_by = '',
                context_type = 'agent',
                context_id = 56,
        )
        """

    def testAttachContextToDesignDocReq(self):
        """Test AttachContextToDesignDocReq"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
