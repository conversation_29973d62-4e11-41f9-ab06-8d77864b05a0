# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_security_review_service_client.models.body_reprocess_design_doc import BodyReprocessDesignDoc

class TestBodyReprocessDesignDoc(unittest.TestCase):
    """BodyReprocessDesignDoc unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> BodyReprocessDesignDoc:
        """Test BodyReprocessDesignDoc
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `BodyReprocessDesignDoc`
        """
        model = BodyReprocessDesignDoc()
        if include_optional:
            return BodyReprocessDesignDoc(
                created_by = ''
            )
        else:
            return BodyReprocessDesignDoc(
                created_by = '',
        )
        """

    def testBodyReprocessDesignDoc(self):
        """Test BodyReprocessDesignDoc"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
