# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_security_review_service_client.models.job_created_response import JobCreatedResponse

class TestJobCreatedResponse(unittest.TestCase):
    """JobCreatedResponse unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> JobCreatedResponse:
        """Test JobCreatedResponse
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `JobCreatedResponse`
        """
        model = JobCreatedResponse()
        if include_optional:
            return JobCreatedResponse(
                job_id = 56,
                status = ''
            )
        else:
            return JobCreatedResponse(
                job_id = 56,
                status = '',
        )
        """

    def testJobCreatedResponse(self):
        """Test JobCreatedResponse"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
