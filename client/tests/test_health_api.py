# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_security_review_service_client.api.health_api import HealthApi


class TestHealthApi(unittest.IsolatedAsyncioTestCase):
    """HealthApi unit test stubs"""

    async def asyncSetUp(self) -> None:
        self.api = HealthApi()

    async def asyncTearDown(self) -> None:
        await self.api.api_client.close()

    async def test_is_alive(self) -> None:
        """Test case for is_alive

        Is Alive
        """
        pass


if __name__ == '__main__':
    unittest.main()
