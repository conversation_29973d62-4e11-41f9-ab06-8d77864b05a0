# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_security_review_service_client.models.design_doc_type import DesignDocType

class TestDesignDocType(unittest.TestCase):
    """DesignDocType unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def testDesignDocType(self):
        """Test DesignDocType"""
        # inst = DesignDocType()

if __name__ == '__main__':
    unittest.main()
