# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_security_review_service_client.api.design_docs_api import DesignDocsApi


class TestDesignDocsApi(unittest.IsolatedAsyncioTestCase):
    """DesignDocsApi unit test stubs"""

    async def asyncSetUp(self) -> None:
        self.api = DesignDocsApi()

    async def asyncTearDown(self) -> None:
        await self.api.api_client.close()

    async def test_attach_context_to_design_doc(self) -> None:
        """Test case for attach_context_to_design_doc

        Attach Context To Design Doc
        """
        pass

    async def test_create_design_doc_container(self) -> None:
        """Test case for create_design_doc_container

        Create Design Doc Container
        """
        pass

    async def test_create_design_docs_by_reference(self) -> None:
        """Test case for create_design_docs_by_reference

        Create Design Docs By Reference
        """
        pass

    async def test_delete_design_doc(self) -> None:
        """Test case for delete_design_doc

        Delete Design Doc
        """
        pass

    async def test_design_docs_by_confluence(self) -> None:
        """Test case for design_docs_by_confluence

        Design Docs By Confluence
        """
        pass

    async def test_design_docs_by_file(self) -> None:
        """Test case for design_docs_by_file

        Design Docs By File
        """
        pass

    async def test_design_docs_by_gdrive(self) -> None:
        """Test case for design_docs_by_gdrive

        Design Docs By Gdrive
        """
        pass

    async def test_design_docs_by_urls(self) -> None:
        """Test case for design_docs_by_urls

        Design Docs By Urls
        """
        pass

    async def test_download_design_doc(self) -> None:
        """Test case for download_design_doc

        Download Design Doc
        """
        pass

    async def test_get_design_doc_by_id(self) -> None:
        """Test case for get_design_doc_by_id

        Get Design Doc By Id
        """
        pass

    async def test_get_design_docs(self) -> None:
        """Test case for get_design_docs

        Get-Design-Docs
        """
        pass

    async def test_reprocess_design_doc(self) -> None:
        """Test case for reprocess_design_doc

        Reprocess Design Doc
        """
        pass

    async def test_update_design_doc_by_file(self) -> None:
        """Test case for update_design_doc_by_file

        Update Design Doc By File
        """
        pass


if __name__ == '__main__':
    unittest.main()
