# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_security_review_service_client.models.job_status_response import JobStatusResponse

class TestJobStatusResponse(unittest.TestCase):
    """JobStatusResponse unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> JobStatusResponse:
        """Test JobStatusResponse
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `JobStatusResponse`
        """
        model = JobStatusResponse()
        if include_optional:
            return JobStatusResponse(
                name = 'design_docs',
                status = 'SCHEDULED',
                error = '',
                id = 56,
                created_at = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'),
                progress = 56,
                source_id = 56,
                created_by = ''
            )
        else:
            return JobStatusResponse(
                name = 'design_docs',
                status = 'SCHEDULED',
                error = '',
                id = 56,
                created_at = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'),
                progress = 56,
                source_id = 56,
                created_by = '',
        )
        """

    def testJobStatusResponse(self):
        """Test JobStatusResponse"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
