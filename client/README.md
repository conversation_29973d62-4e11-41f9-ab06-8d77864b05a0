# prime-security-review-service-client
Service API

This Python package is automatically generated by the [OpenAPI Generator](https://openapi-generator.tech) project:

- API version: 1.0.0
- Package version: 1.0.0
- Generator version: 7.12.0
- Build package: org.openapitools.codegen.languages.PythonClientCodegen

## Requirements.

Python 3.8+

## Installation & Usage
### pip install

If the python package is hosted on a repository, you can install directly using:

```sh
pip install git+https://github.com/GIT_USER_ID/GIT_REPO_ID.git
```
(you may need to run `pip` with root permission: `sudo pip install git+https://github.com/GIT_USER_ID/GIT_REPO_ID.git`)

Then import the package:
```python
import prime_security_review_service_client
```

### Setuptools

Install via [Setuptools](http://pypi.python.org/pypi/setuptools).

```sh
python setup.py install --user
```
(or `sudo python setup.py install` to install the package for all users)

Then import the package:
```python
import prime_security_review_service_client
```

### Tests

Execute `pytest` to run the tests.

## Getting Started

Please follow the [installation procedure](#installation--usage) and then run the following:

```python

import prime_security_review_service_client
from prime_security_review_service_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost
# See configuration.py for a list of all supported configuration parameters.
configuration = prime_security_review_service_client.Configuration(
    host = "http://localhost"
)



# Enter a context with an instance of the API client
async with prime_security_review_service_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = prime_security_review_service_client.DesignDocsApi(api_client)
    account_id = 'account_id_example' # str | Account ID
    doc_id = 56 # int | 
    attach_context_to_design_doc_req = prime_security_review_service_client.AttachContextToDesignDocReq() # AttachContextToDesignDocReq | 

    try:
        # Attach Context To Design Doc
        api_response = await api_instance.attach_context_to_design_doc(account_id, doc_id, attach_context_to_design_doc_req)
        print("The response of DesignDocsApi->attach_context_to_design_doc:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling DesignDocsApi->attach_context_to_design_doc: %s\n" % e)

```

## Documentation for API Endpoints

All URIs are relative to *http://localhost*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
*DesignDocsApi* | [**attach_context_to_design_doc**](docs/DesignDocsApi.md#attach_context_to_design_doc) | **POST** /design-docs/{account_id}/{doc_id}/attach-context | Attach Context To Design Doc
*DesignDocsApi* | [**create_design_doc_container**](docs/DesignDocsApi.md#create_design_doc_container) | **POST** /design-docs/{account_id}/case-id/{case_id} | Create Design Doc Container
*DesignDocsApi* | [**create_design_docs_by_reference**](docs/DesignDocsApi.md#create_design_docs_by_reference) | **POST** /design-docs/{account_id}/reference | Create Design Docs By Reference
*DesignDocsApi* | [**delete_design_doc**](docs/DesignDocsApi.md#delete_design_doc) | **DELETE** /design-docs/{account_id}/delete/{doc_id} | Delete Design Doc
*DesignDocsApi* | [**design_docs_by_confluence**](docs/DesignDocsApi.md#design_docs_by_confluence) | **POST** /design-docs/{account_id}/confluence | Design Docs By Confluence
*DesignDocsApi* | [**design_docs_by_file**](docs/DesignDocsApi.md#design_docs_by_file) | **POST** /design-docs/{account_id}/file | Design Docs By File
*DesignDocsApi* | [**design_docs_by_gdrive**](docs/DesignDocsApi.md#design_docs_by_gdrive) | **POST** /design-docs/{account_id}/gdrive | Design Docs By Gdrive
*DesignDocsApi* | [**design_docs_by_urls**](docs/DesignDocsApi.md#design_docs_by_urls) | **POST** /design-docs/{account_id}/urls | Design Docs By Urls
*DesignDocsApi* | [**download_design_doc**](docs/DesignDocsApi.md#download_design_doc) | **GET** /design-docs/{account_id}/download/{doc_id} | Download Design Doc
*DesignDocsApi* | [**get_design_doc_by_id**](docs/DesignDocsApi.md#get_design_doc_by_id) | **GET** /design-docs/{account_id}/{doc_id} | Get Design Doc By Id
*DesignDocsApi* | [**get_design_docs**](docs/DesignDocsApi.md#get_design_docs) | **GET** /design-docs/{account_id} | Get-Design-Docs
*DesignDocsApi* | [**get_security_review_by_container**](docs/DesignDocsApi.md#get_security_review_by_container) | **GET** /design-docs/{account_id}/case-id/{case_id} | Get Security Review By Container
*DesignDocsApi* | [**reprocess_design_doc**](docs/DesignDocsApi.md#reprocess_design_doc) | **POST** /design-docs/{account_id}/{doc_id}/reprocess | Reprocess Design Doc
*DesignDocsApi* | [**update_design_doc_by_file**](docs/DesignDocsApi.md#update_design_doc_by_file) | **PUT** /design-docs/{account_id}/file/{doc_id} | Update Design Doc By File
*HealthApi* | [**is_alive**](docs/HealthApi.md#is_alive) | **GET** /is-alive | Is Alive
*JobsApi* | [**add_job**](docs/JobsApi.md#add_job) | **POST** /jobs/{account_id}/ | Add Job
*JobsApi* | [**get_job**](docs/JobsApi.md#get_job) | **GET** /jobs/{account_id}/{job_id} | Get Job
*JobsApi* | [**get_jobs**](docs/JobsApi.md#get_jobs) | **GET** /jobs/{account_id} | Get Jobs


## Documentation For Models

 - [AttachContextToDesignDocReq](docs/AttachContextToDesignDocReq.md)
 - [AttackScenario](docs/AttackScenario.md)
 - [BodyCreateDesignDocContainer](docs/BodyCreateDesignDocContainer.md)
 - [BodyCreateDesignDocsByReference](docs/BodyCreateDesignDocsByReference.md)
 - [BodyReprocessDesignDoc](docs/BodyReprocessDesignDoc.md)
 - [ContextType](docs/ContextType.md)
 - [DesignDocByMultipleUrlsReq](docs/DesignDocByMultipleUrlsReq.md)
 - [DesignDocBySingleUrlReq](docs/DesignDocBySingleUrlReq.md)
 - [DesignDocFileUploadResponse](docs/DesignDocFileUploadResponse.md)
 - [DesignDocPolicyRecommendationQuote](docs/DesignDocPolicyRecommendationQuote.md)
 - [DesignDocProcessStatus](docs/DesignDocProcessStatus.md)
 - [DesignDocResponse](docs/DesignDocResponse.md)
 - [DesignDocTopPolicyRecommendation](docs/DesignDocTopPolicyRecommendation.md)
 - [DesignDocTopRecommendation](docs/DesignDocTopRecommendation.md)
 - [DesignDocType](docs/DesignDocType.md)
 - [ExtendDesignDocResponse](docs/ExtendDesignDocResponse.md)
 - [HTTPValidationError](docs/HTTPValidationError.md)
 - [IsAliveResponse](docs/IsAliveResponse.md)
 - [JobCreatedResponse](docs/JobCreatedResponse.md)
 - [JobDesignDocsCreateArgs](docs/JobDesignDocsCreateArgs.md)
 - [JobStatus](docs/JobStatus.md)
 - [JobStatusResponse](docs/JobStatusResponse.md)
 - [JobType](docs/JobType.md)
 - [LocationInner](docs/LocationInner.md)
 - [OrderBy](docs/OrderBy.md)
 - [OrderDirection](docs/OrderDirection.md)
 - [PaginationResponseDesignDocResponse](docs/PaginationResponseDesignDocResponse.md)
 - [ValidationError](docs/ValidationError.md)


<a id="documentation-for-authorization"></a>
## Documentation For Authorization

Endpoints do not require authorization.


## Author




