{"openapi": "3.0.3", "info": {"title": "Service API ", "description": "Service API", "version": "1.0.0"}, "paths": {"/design-docs/{account_id}": {"get": {"tags": ["design-docs"], "summary": "Get-Design-Docs", "description": "Get all design docs", "operationId": "get-design-docs", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "doc_source_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/DesignDocType"}, {"type": "null"}], "title": "Doc Source Type"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "exclusiveMaximum": 10000, "default": 100, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginationResponse_DesignDocResponse_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/design-docs/{account_id}/delete/{doc_id}": {"delete": {"tags": ["design-docs"], "summary": "Delete Design Doc", "operationId": "delete_design_doc", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "doc_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Doc Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/design-docs/{account_id}/download/{doc_id}": {"get": {"tags": ["design-docs"], "summary": "Download Design Doc", "operationId": "download_design_doc", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "doc_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Doc Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "string", "format": "binary", "title": "Response Download Design Doc"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/design-docs/{account_id}/{doc_id}": {"get": {"tags": ["design-docs"], "summary": "Get Design Doc By Id", "operationId": "get_design_doc_by_id", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "doc_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Doc Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExtendDesignDocResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/design-docs/{account_id}/{doc_id}/reprocess": {"post": {"tags": ["design-docs"], "summary": "Reprocess Design Doc", "operationId": "reprocess_design_doc", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "doc_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Doc Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_reprocess_design_doc"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/design-docs/{account_id}/{doc_id}/attach-context": {"post": {"tags": ["design-docs"], "summary": "Attach Context To Design Doc", "operationId": "attach_context_to_design_doc", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "doc_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Doc Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttachContextToDesignDocReq"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/design-docs/{account_id}/file": {"post": {"tags": ["design-docs"], "summary": "Design Docs By File", "operationId": "design_docs_by_file", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "created_by", "in": "query", "required": true, "schema": {"type": "string", "title": "Created By"}}, {"name": "process_as_one", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Process As One"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_design_docs_by_file"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DesignDocFileUploadResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/design-docs/{account_id}/file/{doc_id}": {"put": {"tags": ["design-docs"], "summary": "Update Design Doc By File", "operationId": "update_design_doc_by_file", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "doc_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Doc Id"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_update_design_doc_by_file"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DesignDocFileUploadResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/design-docs/{account_id}/gdrive": {"post": {"tags": ["design-docs"], "summary": "Design Docs By Gdrive", "operationId": "design_docs_by_gdrive", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DesignDocBySingleUrlReq"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DesignDocFileUploadResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/design-docs/{account_id}/confluence": {"post": {"tags": ["design-docs"], "summary": "Design Docs By Confluence", "operationId": "design_docs_by_confluence", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DesignDocBySingleUrlReq"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DesignDocFileUploadResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/design-docs/{account_id}/reference": {"post": {"tags": ["design-docs"], "summary": "Create Design Docs By Reference", "operationId": "create_design_docs_by_reference", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_create_design_docs_by_reference"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/design-docs/{account_id}/urls": {"post": {"tags": ["design-docs"], "summary": "Design Docs By Urls", "operationId": "design_docs_by_urls", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DesignDocByMultipleUrlsReq"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DesignDocFileUploadResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/design-docs/{account_id}/case-id/{case_id}": {"post": {"tags": ["design-docs"], "summary": "Create Design Doc Container", "operationId": "create_design_doc_container", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "case_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "Case ID", "title": "Case Id"}, "description": "Case ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_create_design_doc_container"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DesignDocFileUploadResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/jobs/{account_id}/{job_id}": {"get": {"tags": ["jobs"], "summary": "Get Job", "description": "Get job", "operationId": "get_job", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "job_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Job Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobStatusResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/jobs/{account_id}": {"get": {"tags": ["jobs"], "summary": "Get Jobs", "description": "Get jobs", "operationId": "get_jobs", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "job_status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/JobStatus"}}, {"type": "null"}], "title": "Job Status"}}, {"name": "source_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Source Id"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "exclusiveMaximum": 10000, "default": 100, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}, {"name": "include_deleted", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Include Deleted"}}, {"name": "order_by", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/OrderBy", "default": "created_at"}}, {"name": "order_direction", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/OrderDirection", "default": "desc"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/JobStatusResponse"}, "title": "Response Get Jobs"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/jobs/{account_id}/": {"post": {"tags": ["jobs"], "summary": "Add Job", "description": "Adds a job for the given account and arguments", "operationId": "add_job", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobDesignDocsCreateArgs"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobCreatedResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/is-alive": {"get": {"tags": ["health"], "summary": "Is Alive", "description": "returns the servers localtime, uptime and hit-counter.", "operationId": "is_alive", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IsAliveResponse"}}}}}}}}, "components": {"schemas": {"AttachContextToDesignDocReq": {"properties": {"created_by": {"type": "string", "title": "Created By"}, "context_type": {"$ref": "#/components/schemas/ContextType"}, "context_id": {"type": "integer", "title": "Context Id"}}, "type": "object", "required": ["created_by", "context_type", "context_id"], "title": "AttachContextToDesignDocReq"}, "AttackScenario": {"properties": {"id": {"type": "integer", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "markdown": {"type": "string", "title": "<PERSON><PERSON>"}, "dataflow_diagram": {"type": "string", "title": "Dataflow Diagram"}, "recommendations": {"items": {"$ref": "#/components/schemas/DesignDocTopRecommendation"}, "type": "array", "title": "Recommendations"}}, "type": "object", "required": ["id", "name", "markdown", "dataflow_diagram", "recommendations"], "title": "AttackScenario"}, "Body_create_design_doc_container": {"properties": {"created_by": {"type": "string", "title": "Created By"}}, "type": "object", "required": ["created_by"], "title": "Body_create_design_doc_container"}, "Body_create_design_docs_by_reference": {"properties": {"creator": {"type": "string", "title": "Creator"}, "url": {"type": "string", "title": "Url"}, "case_id": {"type": "integer", "title": "Case Id"}}, "type": "object", "required": ["creator", "url", "case_id"], "title": "Body_create_design_docs_by_reference"}, "Body_design_docs_by_file": {"properties": {"design_doc_files": {"items": {"type": "string", "format": "binary"}, "type": "array", "title": "Design Doc Files"}}, "type": "object", "required": ["design_doc_files"], "title": "Body_design_docs_by_file"}, "Body_reprocess_design_doc": {"properties": {"created_by": {"type": "string", "title": "Created By"}}, "type": "object", "required": ["created_by"], "title": "Body_reprocess_design_doc"}, "Body_update_design_doc_by_file": {"properties": {"design_doc_file": {"type": "string", "format": "binary", "title": "Design Doc File"}}, "type": "object", "required": ["design_doc_file"], "title": "Body_update_design_doc_by_file"}, "ContextType": {"type": "string", "enum": ["agent"], "title": "ContextType"}, "DesignDocByMultipleUrlsReq": {"properties": {"urls": {"items": {"type": "string"}, "type": "array", "title": "Urls"}, "process_as_one": {"type": "boolean", "title": "Process As One", "default": false}, "created_by": {"type": "string", "title": "Created By"}}, "type": "object", "required": ["urls", "created_by"], "title": "DesignDocByMultipleUrlsReq"}, "DesignDocBySingleUrlReq": {"properties": {"created_by": {"type": "string", "title": "Created By"}, "url": {"type": "string", "title": "Url"}}, "type": "object", "required": ["created_by", "url"], "title": "DesignDocBySingleUrlReq"}, "DesignDocFileUploadResponse": {"properties": {"files_uploaded": {"type": "integer", "title": "Files Uploaded"}, "file_names": {"items": {"type": "string"}, "type": "array", "title": "File Names"}}, "type": "object", "required": ["files_uploaded", "file_names"], "title": "DesignDocFileUploadResponse"}, "DesignDocPolicyRecommendationQuote": {"properties": {"quote_text": {"type": "string", "title": "Quote Text"}, "quote_source": {"type": "string", "title": "Quote Source"}}, "type": "object", "required": ["quote_text", "quote_source"], "title": "DesignDocPolicyRecommendationQuote"}, "DesignDocProcessStatus": {"properties": {"processing_progress_percent": {"type": "number", "title": "Processing Progress Percent"}, "job_status": {"$ref": "#/components/schemas/JobStatus"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}}, "type": "object", "required": ["processing_progress_percent", "job_status", "timestamp"], "title": "DesignDocProcessStatus"}, "DesignDocResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "title": {"type": "string", "title": "Title"}, "case_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Case Id"}, "created_by": {"type": "string", "title": "Created By"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "file_origin_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "File Origin Id"}, "url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Url"}, "issue_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Issue Id"}, "source_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Source Id"}, "doc_source_type": {"$ref": "#/components/schemas/DesignDocType"}, "jira_issue_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Jira Issue Url"}, "processing_status": {"anyOf": [{"$ref": "#/components/schemas/DesignDocProcessStatus"}, {"type": "null"}]}}, "type": "object", "required": ["id", "title", "created_by", "created_at", "updated_at", "file_origin_id", "doc_source_type"], "title": "DesignDocResponse"}, "DesignDocTopPolicyRecommendation": {"properties": {"id": {"type": "integer", "title": "Id"}, "title": {"type": "string", "title": "Title"}, "description": {"type": "string", "title": "Description"}, "evidence": {"type": "string", "title": "Evidence"}, "references": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "title": "References"}, "component": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Component"}, "concern": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Concern"}, "quotes": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Quotes"}, "attack_scenarios_names": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Attack Scenarios Names"}, "policy_quotes": {"items": {"$ref": "#/components/schemas/DesignDocPolicyRecommendationQuote"}, "type": "array", "title": "Policy Quotes"}}, "type": "object", "required": ["id", "title", "description", "evidence", "policy_quotes"], "title": "DesignDocTopPolicyRecommendation"}, "DesignDocTopRecommendation": {"properties": {"id": {"type": "integer", "title": "Id"}, "title": {"type": "string", "title": "Title"}, "description": {"type": "string", "title": "Description"}, "evidence": {"type": "string", "title": "Evidence"}, "references": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "title": "References"}, "component": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Component"}, "concern": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Concern"}, "quotes": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Quotes"}, "attack_scenarios_names": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Attack Scenarios Names"}}, "type": "object", "required": ["id", "title", "description", "evidence"], "title": "DesignDocTopRecommendation"}, "DesignDocType": {"type": "string", "enum": ["original", "reference", "container", "url"], "title": "DesignDocType"}, "ExtendDesignDocResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "title": {"type": "string", "title": "Title"}, "case_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Case Id"}, "created_by": {"type": "string", "title": "Created By"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "file_origin_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "File Origin Id"}, "url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Url"}, "issue_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Issue Id"}, "source_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Source Id"}, "doc_source_type": {"$ref": "#/components/schemas/DesignDocType"}, "jira_issue_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Jira Issue Url"}, "processing_status": {"anyOf": [{"$ref": "#/components/schemas/DesignDocProcessStatus"}, {"type": "null"}]}, "summary": {"type": "string", "title": "Summary"}, "top_recommendations": {"items": {"$ref": "#/components/schemas/DesignDocTopRecommendation"}, "type": "array", "title": "Top Recommendations", "default": []}, "policy_recommendations": {"items": {"$ref": "#/components/schemas/DesignDocTopPolicyRecommendation"}, "type": "array", "title": "Policy Recommendations", "default": []}, "mermaid_diagram": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Mermaid Diagram"}, "attack_scenarios": {"anyOf": [{"items": {"$ref": "#/components/schemas/AttackScenario"}, "type": "array"}, {"type": "null"}], "title": "Attack Scenarios"}, "dataflow_diagram": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Dataflow Diagram"}}, "type": "object", "required": ["id", "title", "created_by", "created_at", "updated_at", "file_origin_id", "doc_source_type", "summary"], "title": "ExtendDesignDocResponse"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "IsAliveResponse": {"properties": {"now": {"type": "string", "format": "date-time", "title": "Now"}, "uptime": {"type": "string", "title": "Uptime"}, "hit_count": {"type": "integer", "title": "Hit Count"}}, "type": "object", "required": ["now", "uptime", "hit_count"], "title": "IsAliveResponse"}, "JobCreatedResponse": {"properties": {"job_id": {"type": "integer", "title": "Job Id"}, "status": {"type": "string", "title": "Status"}}, "type": "object", "required": ["job_id", "status"], "title": "JobCreatedResponse"}, "JobDesignDocsCreateArgs": {"properties": {"job": {"$ref": "#/components/schemas/JobType", "default": "design_docs"}, "created_by": {"type": "string", "title": "Created By"}, "force": {"type": "boolean", "title": "Force", "default": false}, "design_doc_ids": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "title": "Design Doc Ids"}}, "type": "object", "required": ["created_by"], "title": "JobDesignDocsCreateArgs"}, "JobStatus": {"type": "string", "enum": ["SCHEDULED", "PENDING", "RUNNING", "FINALIZING", "COMPLETED", "FAILED", "CANCELED"], "title": "JobStatus"}, "JobStatusResponse": {"properties": {"name": {"$ref": "#/components/schemas/JobType"}, "status": {"$ref": "#/components/schemas/JobStatus"}, "error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "progress": {"type": "integer", "title": "Progress"}, "source_id": {"type": "integer", "title": "Source Id"}, "created_by": {"type": "string", "title": "Created By"}}, "type": "object", "required": ["name", "status", "error", "id", "created_at", "progress", "source_id", "created_by"], "title": "JobStatusResponse"}, "JobType": {"type": "string", "enum": ["design_docs"], "title": "JobType"}, "OrderBy": {"type": "string", "enum": ["created_at", "modified_at", "deleted_at"], "title": "OrderBy"}, "OrderDirection": {"type": "string", "enum": ["asc", "desc"], "title": "OrderDirection"}, "PaginationResponse_DesignDocResponse_": {"properties": {"results": {"items": {"$ref": "#/components/schemas/DesignDocResponse"}, "type": "array", "title": "Results"}, "size": {"type": "integer", "title": "Size"}, "limit": {"type": "integer", "title": "Limit"}, "start": {"type": "integer", "title": "Start"}, "total": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Total"}, "has_next": {"type": "boolean", "title": "Has Next", "default": false}}, "type": "object", "required": ["results", "size", "limit", "start"], "title": "PaginationResponse[DesignDocResponse]"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}