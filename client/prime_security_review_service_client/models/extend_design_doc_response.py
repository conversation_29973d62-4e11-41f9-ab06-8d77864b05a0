# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from prime_security_review_service_client.models.attack_scenario import AttackScenario
from prime_security_review_service_client.models.design_doc_process_status import DesignDocProcessStatus
from prime_security_review_service_client.models.design_doc_top_policy_recommendation import DesignDocTopPolicyRecommendation
from prime_security_review_service_client.models.design_doc_top_recommendation import DesignDocTopRecommendation
from prime_security_review_service_client.models.design_doc_type import DesignDocType
from typing import Optional, Set
from typing_extensions import Self

class ExtendDesignDocResponse(BaseModel):
    """
    ExtendDesignDocResponse
    """ # noqa: E501
    id: StrictInt
    title: StrictStr
    case_id: Optional[StrictInt] = None
    created_by: StrictStr
    created_at: datetime
    updated_at: datetime
    file_origin_id: Optional[StrictStr]
    url: Optional[StrictStr] = None
    issue_id: Optional[StrictStr] = None
    source_id: Optional[StrictInt] = None
    doc_source_type: DesignDocType
    jira_issue_url: Optional[StrictStr] = None
    processing_status: Optional[DesignDocProcessStatus] = None
    summary: StrictStr
    top_recommendations: Optional[List[DesignDocTopRecommendation]] = None
    policy_recommendations: Optional[List[DesignDocTopPolicyRecommendation]] = None
    mermaid_diagram: Optional[StrictStr] = None
    attack_scenarios: Optional[List[AttackScenario]] = None
    dataflow_diagram: Optional[StrictStr] = None
    __properties: ClassVar[List[str]] = ["id", "title", "case_id", "created_by", "created_at", "updated_at", "file_origin_id", "url", "issue_id", "source_id", "doc_source_type", "jira_issue_url", "processing_status", "summary", "top_recommendations", "policy_recommendations", "mermaid_diagram", "attack_scenarios", "dataflow_diagram"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of ExtendDesignDocResponse from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of processing_status
        if self.processing_status:
            _dict['processing_status'] = self.processing_status.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each item in top_recommendations (list)
        _items = []
        if self.top_recommendations:
            for _item_top_recommendations in self.top_recommendations:
                if _item_top_recommendations:
                    _items.append(_item_top_recommendations.to_dict())
            _dict['top_recommendations'] = _items
        # override the default output from pydantic by calling `to_dict()` of each item in policy_recommendations (list)
        _items = []
        if self.policy_recommendations:
            for _item_policy_recommendations in self.policy_recommendations:
                if _item_policy_recommendations:
                    _items.append(_item_policy_recommendations.to_dict())
            _dict['policy_recommendations'] = _items
        # override the default output from pydantic by calling `to_dict()` of each item in attack_scenarios (list)
        _items = []
        if self.attack_scenarios:
            for _item_attack_scenarios in self.attack_scenarios:
                if _item_attack_scenarios:
                    _items.append(_item_attack_scenarios.to_dict())
            _dict['attack_scenarios'] = _items
        # set to None if case_id (nullable) is None
        # and model_fields_set contains the field
        if self.case_id is None and "case_id" in self.model_fields_set:
            _dict['case_id'] = None

        # set to None if file_origin_id (nullable) is None
        # and model_fields_set contains the field
        if self.file_origin_id is None and "file_origin_id" in self.model_fields_set:
            _dict['file_origin_id'] = None

        # set to None if url (nullable) is None
        # and model_fields_set contains the field
        if self.url is None and "url" in self.model_fields_set:
            _dict['url'] = None

        # set to None if issue_id (nullable) is None
        # and model_fields_set contains the field
        if self.issue_id is None and "issue_id" in self.model_fields_set:
            _dict['issue_id'] = None

        # set to None if source_id (nullable) is None
        # and model_fields_set contains the field
        if self.source_id is None and "source_id" in self.model_fields_set:
            _dict['source_id'] = None

        # set to None if jira_issue_url (nullable) is None
        # and model_fields_set contains the field
        if self.jira_issue_url is None and "jira_issue_url" in self.model_fields_set:
            _dict['jira_issue_url'] = None

        # set to None if processing_status (nullable) is None
        # and model_fields_set contains the field
        if self.processing_status is None and "processing_status" in self.model_fields_set:
            _dict['processing_status'] = None

        # set to None if mermaid_diagram (nullable) is None
        # and model_fields_set contains the field
        if self.mermaid_diagram is None and "mermaid_diagram" in self.model_fields_set:
            _dict['mermaid_diagram'] = None

        # set to None if attack_scenarios (nullable) is None
        # and model_fields_set contains the field
        if self.attack_scenarios is None and "attack_scenarios" in self.model_fields_set:
            _dict['attack_scenarios'] = None

        # set to None if dataflow_diagram (nullable) is None
        # and model_fields_set contains the field
        if self.dataflow_diagram is None and "dataflow_diagram" in self.model_fields_set:
            _dict['dataflow_diagram'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of ExtendDesignDocResponse from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "id": obj.get("id"),
            "title": obj.get("title"),
            "case_id": obj.get("case_id"),
            "created_by": obj.get("created_by"),
            "created_at": obj.get("created_at"),
            "updated_at": obj.get("updated_at"),
            "file_origin_id": obj.get("file_origin_id"),
            "url": obj.get("url"),
            "issue_id": obj.get("issue_id"),
            "source_id": obj.get("source_id"),
            "doc_source_type": obj.get("doc_source_type"),
            "jira_issue_url": obj.get("jira_issue_url"),
            "processing_status": DesignDocProcessStatus.from_dict(obj["processing_status"]) if obj.get("processing_status") is not None else None,
            "summary": obj.get("summary"),
            "top_recommendations": [DesignDocTopRecommendation.from_dict(_item) for _item in obj["top_recommendations"]] if obj.get("top_recommendations") is not None else None,
            "policy_recommendations": [DesignDocTopPolicyRecommendation.from_dict(_item) for _item in obj["policy_recommendations"]] if obj.get("policy_recommendations") is not None else None,
            "mermaid_diagram": obj.get("mermaid_diagram"),
            "attack_scenarios": [AttackScenario.from_dict(_item) for _item in obj["attack_scenarios"]] if obj.get("attack_scenarios") is not None else None,
            "dataflow_diagram": obj.get("dataflow_diagram")
        })
        return _obj


