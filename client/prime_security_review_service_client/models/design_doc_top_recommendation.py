# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class DesignDocTopRecommendation(BaseModel):
    """
    DesignDocTopRecommendation
    """ # noqa: E501
    id: StrictInt
    title: StrictStr
    description: StrictStr
    evidence: StrictStr
    references: Optional[List[StrictInt]] = None
    component: Optional[StrictStr] = None
    concern: Optional[StrictStr] = None
    quotes: Optional[List[StrictStr]] = None
    attack_scenarios_names: Optional[List[StrictStr]] = None
    __properties: ClassVar[List[str]] = ["id", "title", "description", "evidence", "references", "component", "concern", "quotes", "attack_scenarios_names"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of DesignDocTopRecommendation from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # set to None if references (nullable) is None
        # and model_fields_set contains the field
        if self.references is None and "references" in self.model_fields_set:
            _dict['references'] = None

        # set to None if component (nullable) is None
        # and model_fields_set contains the field
        if self.component is None and "component" in self.model_fields_set:
            _dict['component'] = None

        # set to None if concern (nullable) is None
        # and model_fields_set contains the field
        if self.concern is None and "concern" in self.model_fields_set:
            _dict['concern'] = None

        # set to None if quotes (nullable) is None
        # and model_fields_set contains the field
        if self.quotes is None and "quotes" in self.model_fields_set:
            _dict['quotes'] = None

        # set to None if attack_scenarios_names (nullable) is None
        # and model_fields_set contains the field
        if self.attack_scenarios_names is None and "attack_scenarios_names" in self.model_fields_set:
            _dict['attack_scenarios_names'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of DesignDocTopRecommendation from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "id": obj.get("id"),
            "title": obj.get("title"),
            "description": obj.get("description"),
            "evidence": obj.get("evidence"),
            "references": obj.get("references"),
            "component": obj.get("component"),
            "concern": obj.get("concern"),
            "quotes": obj.get("quotes"),
            "attack_scenarios_names": obj.get("attack_scenarios_names")
        })
        return _obj


