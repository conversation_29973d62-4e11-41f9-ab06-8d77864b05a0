# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List
from prime_security_review_service_client.models.design_doc_top_recommendation import DesignDocTopRecommendation
from typing import Optional, Set
from typing_extensions import Self

class AttackScenario(BaseModel):
    """
    AttackScenario
    """ # noqa: E501
    id: StrictInt
    name: StrictStr
    markdown: StrictStr
    dataflow_diagram: StrictStr
    recommendations: List[DesignDocTopRecommendation]
    __properties: ClassVar[List[str]] = ["id", "name", "markdown", "dataflow_diagram", "recommendations"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of AttackScenario from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in recommendations (list)
        _items = []
        if self.recommendations:
            for _item_recommendations in self.recommendations:
                if _item_recommendations:
                    _items.append(_item_recommendations.to_dict())
            _dict['recommendations'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of AttackScenario from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "id": obj.get("id"),
            "name": obj.get("name"),
            "markdown": obj.get("markdown"),
            "dataflow_diagram": obj.get("dataflow_diagram"),
            "recommendations": [DesignDocTopRecommendation.from_dict(_item) for _item in obj["recommendations"]] if obj.get("recommendations") is not None else None
        })
        return _obj


